#ifndef VOXEL_MERGE_HPP
#define VOXEL_MERGE_HPP

#include "voxel_map.hpp"
#include "tools.hpp"
#include <unordered_set>
#include <queue>
#include <ros/ros.h>
#include <Eigen/StdVector>  // 支持Eigen容器和对齐分配器
#include <mutex>
#include <atomic>
#include <omp.h>

// 前向声明
class VoxelMergeManager;
// struct MergedVoxelGroup;  // **注释掉：未使用的结构**

// 为pair<VOXEL_LOC, VOXEL_LOC>定义哈希函数
namespace std {
    template<>
    struct hash<pair<VOXEL_LOC, VOXEL_LOC>> {
        size_t operator()(const pair<VOXEL_LOC, VOXEL_LOC>& p) const {
            auto h1 = hash<VOXEL_LOC>{}(p.first);
            auto h2 = hash<VOXEL_LOC>{}(p.second);
            return h1 ^ (h2 << 1);
        }
    };
}

// 体素更新跟踪器
class VoxelUpdateTracker {
private:
    unordered_set<VOXEL_LOC> newly_created_voxels;    // 新创建的体素
    unordered_set<VOXEL_LOC> subdivided_voxels;       // 细分产生的体素
    unordered_set<VOXEL_LOC> updated_voxels;          // 接收新数据的体素
    unordered_set<VOXEL_LOC> affected_neighbors;      // 受影响的邻居体素
    unordered_set<VOXEL_LOC> voxels_need_merge_reset; // 需要重置合并状态的体素

public:
    void mark_voxel_created(const VOXEL_LOC &loc) {
        newly_created_voxels.insert(loc);
    }

    void mark_voxel_subdivided(const VOXEL_LOC &parent, const vector<VOXEL_LOC> &children) {
        for(const auto &child : children) {
            subdivided_voxels.insert(child);
        }
        // 标记父体素的邻居可能需要重新检查
        mark_neighbors_affected(parent);
    }

    void mark_voxel_updated(const VOXEL_LOC &loc) {
        updated_voxels.insert(loc);
        // **性能优化：标记需要重置合并状态的体素**
        voxels_need_merge_reset.insert(loc);
    }

    void mark_neighbors_affected(const VOXEL_LOC &loc) {
        // 标记6个方向的邻居
        vector<VOXEL_LOC> neighbors = {
            {loc.x+1, loc.y, loc.z}, {loc.x-1, loc.y, loc.z},
            {loc.x, loc.y+1, loc.z}, {loc.x, loc.y-1, loc.z},
            {loc.x, loc.y, loc.z+1}, {loc.x, loc.y, loc.z-1}
        };
        for(const auto &neighbor : neighbors) {
            affected_neighbors.insert(neighbor);
        }
    }

    unordered_set<VOXEL_LOC> get_merge_candidates() {
        unordered_set<VOXEL_LOC> candidates;
        candidates.insert(newly_created_voxels.begin(), newly_created_voxels.end());
        candidates.insert(subdivided_voxels.begin(), subdivided_voxels.end());
        candidates.insert(updated_voxels.begin(), updated_voxels.end());
        candidates.insert(affected_neighbors.begin(), affected_neighbors.end());
        return candidates;
    }

    void clear_tracking_data() {
        newly_created_voxels.clear();
        subdivided_voxels.clear();
        updated_voxels.clear();
        affected_neighbors.clear();
        voxels_need_merge_reset.clear();
    }

    size_t get_total_candidates() const {
        unordered_set<VOXEL_LOC> all_candidates;
        all_candidates.insert(newly_created_voxels.begin(), newly_created_voxels.end());
        all_candidates.insert(subdivided_voxels.begin(), subdivided_voxels.end());
        all_candidates.insert(updated_voxels.begin(), updated_voxels.end());
        all_candidates.insert(affected_neighbors.begin(), affected_neighbors.end());
        return all_candidates.size();
    }
};

// 跨层级邻居搜索器
class HierarchicalNeighborFinder {
public:
    struct NeighborInfo {
        VOXEL_LOC location;
        int hierarchy_level;
        double spatial_distance;
        bool is_spatially_adjacent;
        OctoTree* octree_ptr;

        NeighborInfo(const VOXEL_LOC &loc, int level, double dist, bool adjacent, OctoTree* ptr)
            : location(loc), hierarchy_level(level), spatial_distance(dist),
              is_spatially_adjacent(adjacent), octree_ptr(ptr) {}
    };

    vector<NeighborInfo> find_all_neighbors(
        const VOXEL_LOC &center,    // 中心体素位置
        int center_level,           // 中心体素层级
        const unordered_map<VOXEL_LOC, OctoTree*> &surf_map
    ) {
        vector<NeighborInfo> neighbors;

        // 1. 同层级邻居（6个方向）
        auto same_level_neighbors = get_same_level_neighbors(center);
        for(const auto &neighbor_loc : same_level_neighbors) {
            auto it = surf_map.find(neighbor_loc);
            if(it != surf_map.end() && it->second) {
                neighbors.emplace_back(neighbor_loc, center_level, 1.0, true, it->second);
            }
        }

        // 2. 跨层级邻居搜索
        auto cross_level_candidates = get_cross_level_candidates(center, center_level);
        for(const auto &candidate : cross_level_candidates) {
            auto it = surf_map.find(candidate);
            if(it != surf_map.end() && it->second) {
                int candidate_level = it->second->layer;
                bool is_adjacent = check_spatial_adjacency(center, center_level, candidate, candidate_level);
                if(is_adjacent) {
                    double distance = calculate_spatial_distance(center, center_level, candidate, candidate_level);
                    neighbors.emplace_back(candidate, candidate_level, distance, true, it->second);
                }
            }
        }

        return neighbors;
    }

private:
    vector<VOXEL_LOC> get_same_level_neighbors(const VOXEL_LOC &center) {
        return {
            {center.x+1, center.y, center.z}, {center.x-1, center.y, center.z},
            {center.x, center.y+1, center.z}, {center.x, center.y-1, center.z},
            {center.x, center.y, center.z+1}, {center.x, center.y, center.z-1}
        };
    }

    vector<VOXEL_LOC> get_cross_level_candidates(const VOXEL_LOC &center, int level) {
        vector<VOXEL_LOC> candidates;

        // 搜索不同层级的可能邻居
        for(int target_level = 0; target_level <= 2; ++target_level) {
            if(target_level == level) continue;

            if(target_level > level) {
                // 搜索更细分的邻居
                auto fine_candidates = get_finer_level_candidates(center, level, target_level);
                candidates.insert(candidates.end(), fine_candidates.begin(), fine_candidates.end());
            } else {
                // 搜索更粗糙的邻居
                auto coarse_candidates = get_coarser_level_candidates(center, level, target_level);
                candidates.insert(candidates.end(), coarse_candidates.begin(), coarse_candidates.end());
            }
        }

        return candidates;
    }

    vector<VOXEL_LOC> get_finer_level_candidates(const VOXEL_LOC &center, int from_level, int to_level) {
        vector<VOXEL_LOC> candidates;
        int scale_factor = 1 << (to_level - from_level);  // 2^(level_diff)

        // 计算当前体素在细分层级的坐标范围
        VOXEL_LOC fine_start = {
            center.x * scale_factor,
            center.y * scale_factor,
            center.z * scale_factor
        };
        VOXEL_LOC fine_end = {
            fine_start.x + scale_factor - 1,
            fine_start.y + scale_factor - 1,
            fine_start.z + scale_factor - 1
        };

        // 搜索6个面上的邻居细分体素
        // X方向的两个面
        for(int j = fine_start.y; j <= fine_end.y; ++j) {
            for(int k = fine_start.z; k <= fine_end.z; ++k) {
                candidates.push_back({fine_start.x - 1, j, k}); // 左面
                candidates.push_back({fine_end.x + 1, j, k});   // 右面
            }
        }
        // Y方向的两个面
        for(int i = fine_start.x; i <= fine_end.x; ++i) {
            for(int k = fine_start.z; k <= fine_end.z; ++k) {
                candidates.push_back({i, fine_start.y - 1, k}); // 前面
                candidates.push_back({i, fine_end.y + 1, k});   // 后面
            }
        }
        // Z方向的两个面
        for(int i = fine_start.x; i <= fine_end.x; ++i) {
            for(int j = fine_start.y; j <= fine_end.y; ++j) {
                candidates.push_back({i, j, fine_start.z - 1}); // 下面
                candidates.push_back({i, j, fine_end.z + 1});   // 上面
            }
        }

        return candidates;
    }

    vector<VOXEL_LOC> get_coarser_level_candidates(const VOXEL_LOC &center, int from_level, int to_level) {
        vector<VOXEL_LOC> candidates;
        int scale_factor = 1 << (from_level - to_level);  // 2^(level_diff)

        // 计算在粗糙层级中的位置
        VOXEL_LOC coarse_center = {
            center.x / scale_factor,
            center.y / scale_factor,
            center.z / scale_factor
        };

        // 搜索粗糙层级的邻居
        auto coarse_neighbors = get_same_level_neighbors(coarse_center);
        candidates.insert(candidates.end(), coarse_neighbors.begin(), coarse_neighbors.end());

        return candidates;
    }

    bool check_spatial_adjacency(const VOXEL_LOC &loc1, int level1,
                                 const VOXEL_LOC &loc2, int level2) {
        // 将两个体素转换到相同的坐标系进行比较
        double scale1 = 1.0 / (1 << level1);
        double scale2 = 1.0 / (1 << level2);

        double x1 = loc1.x * scale1, y1 = loc1.y * scale1, z1 = loc1.z * scale1;
        double x2 = loc2.x * scale2, y2 = loc2.y * scale2, z2 = loc2.z * scale2;

        double size1 = scale1, size2 = scale2;

        // 检查是否在任何方向上相邻
        bool x_adjacent = (abs(x1 - x2) <= (size1 + size2) / 2 + 1e-6) &&
                         (abs(x1 - x2) >= abs(size1 - size2) / 2 - 1e-6);
        bool y_adjacent = (abs(y1 - y2) <= (size1 + size2) / 2 + 1e-6) &&
                         (abs(y1 - y2) >= abs(size1 - size2) / 2 - 1e-6);
        bool z_adjacent = (abs(z1 - z2) <= (size1 + size2) / 2 + 1e-6) &&
                         (abs(z1 - z2) >= abs(size1 - size2) / 2 - 1e-6);

        // 至少在一个方向上相邻，其他方向重叠或相邻
        return (x_adjacent && abs(y1-y2) <= (size1+size2)/2 && abs(z1-z2) <= (size1+size2)/2) ||
               (y_adjacent && abs(x1-x2) <= (size1+size2)/2 && abs(z1-z2) <= (size1+size2)/2) ||
               (z_adjacent && abs(x1-x2) <= (size1+size2)/2 && abs(y1-y2) <= (size1+size2)/2);
    }

    double calculate_spatial_distance(const VOXEL_LOC &loc1, int level1,
                                     const VOXEL_LOC &loc2, int level2) {
        double scale1 = 1.0 / (1 << level1);
        double scale2 = 1.0 / (1 << level2);

        double x1 = loc1.x * scale1, y1 = loc1.y * scale1, z1 = loc1.z * scale1;
        double x2 = loc2.x * scale2, y2 = loc2.y * scale2, z2 = loc2.z * scale2;

        return sqrt((x1-x2)*(x1-x2) + (y1-y2)*(y1-y2) + (z1-z2)*(z1-z2));
    }
};

// **注释掉：TransitiveMergeDetector类，完全未使用**
/*
class TransitiveMergeDetector {
public:
    struct MergeCandidate {
        VOXEL_LOC location;
        int current_group_id;
        vector<int> connectable_groups;

        MergeCandidate(const VOXEL_LOC &loc, int group_id)
            : location(loc), current_group_id(group_id) {}
    };

    vector<vector<int>> detect_mergeable_groups(
        const VOXEL_LOC &center_voxel,
        const vector<HierarchicalNeighborFinder::NeighborInfo> &neighbors,
        const unordered_map<VOXEL_LOC, int> &voxel_to_group
    ) {
        vector<vector<int>> group_chains;
        unordered_set<int> involved_groups;

        // 收集所有相关的合并组
        auto center_it = voxel_to_group.find(center_voxel);
        int center_group = (center_it != voxel_to_group.end()) ? center_it->second : -1;

        if(center_group != -1) {
            involved_groups.insert(center_group);
        }

        for(const auto &neighbor : neighbors) {
            if(neighbor.octree_ptr && neighbor.octree_ptr->is_merged) {
                involved_groups.insert(neighbor.octree_ptr->merge_group_id);
            }
        }

        // 如果有多个组可以连接，创建合并链
        if(involved_groups.size() > 1) {
            vector<int> chain(involved_groups.begin(), involved_groups.end());
            group_chains.push_back(chain);
        }

        return group_chains;
    }

    // 前向声明
    void merge_transitive_groups(const vector<vector<int>> &group_chains,
                                VoxelMergeManager* manager);

private:
    void compute_merged_plane_parameters_for_group(MergedVoxelGroup &group);
};
*/

// **注释掉：MergedVoxelGroup结构，完全未使用**
/*
// 合并体素组结构
struct MergedVoxelGroup {
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW

    vector<VOXEL_LOC> member_voxels;        // 合并的体素位置
    vector<OctoTree*> member_octrees;       // 合并的体素指针
    Plane merged_plane;                     // 合并后的平面参数
    Eigen::Vector3d merged_eig_value;       // 合并后的特征值
    Eigen::Matrix3d merged_eig_vector;      // 合并后的特征向量
    PointCluster merged_pcr_add;            // 合并后的点云聚类
    int merge_id;                           // 合并组ID
    double quality_score;                   // 合并质量评分
    bool is_active = true;                  // 是否激活

    MergedVoxelGroup() {
        merge_id = -1;
        quality_score = 0.0;
        // 不在构造函数中初始化Eigen矩阵，避免内存对齐问题
        // merged_plane.plane_var 会在Plane的构造函数中自动初始化
    }
};
*/

// 合并组控制器
struct MergeGroupController {
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW

    int group_id;

    // **独立存储的优化参数（核心稳定部分）**
    Plane merged_plane;
    Eigen::Vector3d merged_eig_value;
    Eigen::Matrix3d merged_eig_vector;
    PointCluster merged_pcr_add;

    // **🔧 新增：严格按照原始VoxelSLAM的不确定性信息**
    Eigen::Matrix<double, 9, 9> merged_cov_add;  // 对应原始代码的cov_add矩阵

    double quality_score;

    // **体素索引管理（可变部分）**
    unordered_set<VOXEL_LOC> active_members;
    vector<OctoTree*> member_octrees;  // 缓存指针，便于快速访问

    // **状态管理**
    bool is_active;
    bool needs_parameter_refresh;
    int last_update_frame;
    int creation_frame;

    // **添加默认构造函数**
    MergeGroupController() : group_id(-1), quality_score(0.0),
                            is_active(true), needs_parameter_refresh(false),
                            last_update_frame(-1), creation_frame(-1) {
        merged_plane.is_plane = false;
        merged_eig_value.setZero();
        merged_eig_vector.setIdentity();
        merged_cov_add.setZero();  // 初始化cov_add矩阵
    }

    MergeGroupController(int id) : group_id(id), quality_score(0.0),
                                   is_active(true), needs_parameter_refresh(false),
                                   last_update_frame(-1), creation_frame(-1) {
        merged_plane.is_plane = false;
        merged_eig_value.setZero();
        merged_eig_vector.setIdentity();
        merged_cov_add.setZero();  // 初始化cov_add矩阵
    }

    // **核心方法：参数有效性检查**
    bool is_valid_for_optimization() const {
        return is_active && active_members.size() >= 2 && merged_plane.is_plane;
    }

    // **简化的移除成员体素方法**
    void remove_member(const VOXEL_LOC& loc) {
        if(active_members.erase(loc) > 0) {
            // **简单策略：标记需要重新计算，统一重算**
            if(active_members.size() < 2) {
                is_active = false;
            } else {
                needs_parameter_refresh = true;  // 简单标记，统一重算
            }
        }
    }

    // **简化的添加成员体素方法**
    void add_member(const VOXEL_LOC& loc, OctoTree* octree) {
        active_members.insert(loc);
        needs_parameter_refresh = true;  // 简单标记，统一重算
    }

    // **核心方法：刷新参数**
    void refresh_parameters_if_needed(const unordered_map<VOXEL_LOC, OctoTree*>& surf_map) {
        if(!needs_parameter_refresh || active_members.size() < 2) {
            return;
        }

        // 重新收集有效的octree指针
        vector<OctoTree*> valid_octrees;
        for(const auto& loc : active_members) {
            auto it = surf_map.find(loc);
            if(it != surf_map.end() && it->second && it->second->octo_state == 0 &&
               it->second->plane.is_plane && it->second->isexist) {
                valid_octrees.push_back(it->second);
            }
        }

        if(valid_octrees.size() < 2) {
            is_active = false;
            return;
        }

        // 重新计算合并参数
        recompute_merged_parameters(valid_octrees);
        member_octrees = valid_octrees;
        needs_parameter_refresh = false;
    }

private:
    void recompute_merged_parameters(const vector<OctoTree*>& valid_octrees) {
        // **严格按照原始代码的PointCluster模式 - 完整合并**
        merged_pcr_add.clear();
        merged_cov_add.setZero();

        // **步骤1：合并所有体素的PointCluster统计量和cov_add矩阵**
        for(auto* voxel : valid_octrees) {
            merged_pcr_add += voxel->pcr_add;  // 使用PointCluster的+=操作符
            merged_cov_add += voxel->cov_add;  // 🔧 关键：合并cov_add矩阵
        }

        if(merged_pcr_add.N > 0) {
            // **步骤2：从合并后的统计量计算协方差矩阵 - 与原始代码相同**
            Eigen::Matrix3d merged_cov = merged_pcr_add.cov();

            // **步骤3：特征值分解 - 与原始代码完全一致**
            if(!merged_cov.hasNaN() && merged_cov.determinant() > 1e-12) {
                Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> saes(merged_cov);
                if(saes.info() == Eigen::Success) {
                    merged_eig_value = saes.eigenvalues();
                    merged_eig_vector = saes.eigenvectors();

                    // **步骤4：计算平面参数 - 与原始代码逻辑一致**
                    merged_plane.center = merged_pcr_add.v / merged_pcr_add.N;  // 质心
                    merged_plane.normal = merged_eig_vector.col(0);  // 最小特征值对应的特征向量
                    merged_plane.radius = merged_eig_value[2];  // 最大特征值

                    // **🔧 关键修复：计算合并平面的不确定性矩阵**
                    compute_merged_plane_uncertainty();

                    merged_plane.is_plane = true;

                    // **步骤5：质量评估**
                    quality_score = merged_pcr_add.N * (merged_eig_value[2] / (merged_eig_value[0] + 1e-6));
                    is_active = true;
                    return;
                }
            }
        }

        // 如果计算失败，标记为无效
        merged_plane.is_plane = false;
        is_active = false;
    }

private:
    // **🔧 严格按照原始VoxelSLAM数学推导的不确定性计算**
    void compute_merged_plane_uncertainty() {
        // **现在有了完整的merged_cov_add矩阵，可以进行严格的不确定性计算**

        merged_plane.plane_var.setZero();

        // **严格按照原始plane_update函数的数学推导**
        int l = 0;  // 最小特征值索引（法向量对应）
        Eigen::Vector3d u[3] = {merged_eig_vector.col(0), merged_eig_vector.col(1), merged_eig_vector.col(2)};
        double nv = 1.0 / merged_pcr_add.N;

        // **计算u_c矩阵：法向量对协方差矩阵的雅可比**
        Eigen::Matrix<double, 3, 9> u_c;
        u_c.setZero();

        for(int k = 0; k < 3; k++) {
            if(k != l) {
                // **计算ukl = u[k] * u[l]^T**
                Eigen::Matrix3d ukl = u[k] * u[l].transpose();

                // **构造fkl向量（对协方差矩阵的导数）**
                Eigen::Matrix<double, 1, 9> fkl;
                fkl.head(6) << ukl(0, 0), ukl(1, 0)+ukl(0, 1), ukl(2, 0)+ukl(0, 2),
                               ukl(1, 1), ukl(1, 2)+ukl(2, 1), ukl(2, 2);
                fkl.tail(3) = -(u[k].dot(merged_plane.center) * u[l] + u[l].dot(merged_plane.center) * u[k]);

                // **累积u_c矩阵**
                u_c += nv / (merged_eig_value[l] - merged_eig_value[k]) * u[k] * fkl;
            }
        }

        // **计算雅可比矩阵Jc = u_c * merged_cov_add**
        Eigen::Matrix<double, 3, 9> Jc = u_c * merged_cov_add;

        // **严格按照原始公式计算plane_var**
        // 法向量的协方差：plane_var(0:3,0:3) = Jc * u_c^T
        merged_plane.plane_var.block<3, 3>(0, 0) = Jc * u_c.transpose();

        // 法向量与中心的协方差：plane_var(0:3,3:6) = Jc_N
        Eigen::Matrix3d Jc_N = nv * Jc.block<3, 3>(0, 6);
        merged_plane.plane_var.block<3, 3>(0, 3) = Jc_N;
        merged_plane.plane_var.block<3, 3>(3, 0) = Jc_N.transpose();

        // 中心的协方差：plane_var(3:6,3:6) = nv^2 * merged_cov_add(6:9,6:9)
        merged_plane.plane_var.block<3, 3>(3, 3) = nv * nv * merged_cov_add.block<3, 3>(6, 6);

        // **✅ 现在的不确定性计算与原始VoxelSLAM完全一致**
    }
};

// **简化的体素合并管理器**
class VoxelMergeManager {
private:
    static VoxelMergeManager* instance_;

    // **核心：集中参数存储**
    unordered_map<int, MergeGroupController, std::hash<int>, std::equal_to<int>,
                  Eigen::aligned_allocator<std::pair<const int, MergeGroupController>>> group_controllers;
    unordered_map<VOXEL_LOC, int> voxel_to_group;
    int next_group_id = 1;

    // **辅助组件**
    VoxelUpdateTracker update_tracker;

    // **合并参数 - 调整为更保守的阈值**
    static constexpr double NORMAL_ANGLE_THRESHOLD = 3.0;  // 从3度减少到1度，更严格
    static constexpr double DISTANCE_THRESHOLD = 0.03;     // 从3cm减少到1cm，更严格
    //static constexpr double MIN_MERGE_QUALITY = 0.2;       // 提高质量要求

    //!Testing Merge Judge - 新增：防止链式漂移的参数（优化性能）
    static constexpr int MAX_GROUP_SIZE = 50;               // 降低最大组大小，提高性能
    static constexpr bool ENABLE_STRICT_GROUP_VALIDATION = false;  // 🚀 暂时禁用严格验证以提高性能

    // **约束传递去重（每帧重置）**
    unordered_set<int> processed_groups_this_frame;

    //!Testing Merge Judge - 新增：存储当前处理的surf_map和radar_pos供全局验证使用
    unordered_map<VOXEL_LOC, OctoTree*>* current_surf_map = nullptr;
    Eigen::Vector3d current_radar_pos = Eigen::Vector3d::Zero();

    // **约束去重控制**
    static unordered_set<int> contributed_groups_this_ba;
    static std::mutex constraint_mutex;



    VoxelMergeManager() = default;

public:
    // **🚀 新增：预计算数据结构（移到public）**
    struct PrecomputedMergeData {
        vector<PointCluster> merged_pcrs;
        PointCluster merged_pcr_fix;
        bool is_valid;
        int wdsize;

        PrecomputedMergeData() : is_valid(false), wdsize(0) {}
    };

private:
    unordered_map<int, PrecomputedMergeData> precomputed_merge_data;

public:
    static VoxelMergeManager& getInstance() {
        if (!instance_) {
            instance_ = new VoxelMergeManager();
        }
        return *instance_;
    }

    // **主要接口**
    void perform_voxel_merging(unordered_map<VOXEL_LOC, OctoTree*> &surf_map,
                              const Eigen::Vector3d &radar_pos);
    int prepare_for_optimization(unordered_map<VOXEL_LOC, OctoTree*> &surf_map);
    void cleanup_invalid_groups(unordered_map<VOXEL_LOC, OctoTree*> &surf_map);
    void clear_merge_data();

    // **边缘化支持**
    void handle_voxel_removal(const VOXEL_LOC &loc);
    void handle_multiple_voxel_removals(const vector<VOXEL_LOC> &locs);

    // **体素更新跟踪**
    void mark_voxel_created(const VOXEL_LOC &loc) { update_tracker.mark_voxel_created(loc); }
    void mark_voxel_subdivided(const VOXEL_LOC &parent, const vector<VOXEL_LOC> &children) {
        update_tracker.mark_voxel_subdivided(parent, children);
    }
    void mark_voxel_updated(const VOXEL_LOC &loc) { update_tracker.mark_voxel_updated(loc); }
    void clear_updated_voxels() { update_tracker.clear_tracking_data(); }
    size_t get_total_candidates() const { return update_tracker.get_total_candidates(); }

    // **新增：获取合并组成员位置**
    bool get_group_member_locations(int group_id, vector<VOXEL_LOC>& member_locations) {
        auto it = group_controllers.find(group_id);
        if(it == group_controllers.end() || !it->second.is_active) {
            return false;
        }

        member_locations.clear();
        member_locations.reserve(it->second.active_members.size());

        for(const auto& loc : it->second.active_members) {
            member_locations.push_back(loc);
        }

        return !member_locations.empty();
    }

    // **约束传递接口**
    bool should_contribute_constraint(int group_id);
    void reset_frame_constraint_tracking() { processed_groups_this_frame.clear(); }

    // **🔧 新增：验证合并组有效性（公共接口）**
    bool is_group_valid(int group_id) const {
        auto it = group_controllers.find(group_id);
        return (it != group_controllers.end() &&
                it->second.is_active &&
                it->second.active_members.size() >= 2);
    }

    // **🔧 新增：特定组参数刷新方法**
    void refresh_specific_group_parameters(int group_id, const unordered_map<VOXEL_LOC, OctoTree*>& surf_map) {
        auto it = group_controllers.find(group_id);
        if(it != group_controllers.end()) {
            it->second.refresh_parameters_if_needed(surf_map);
        }
    }

    // **🔧 新增：获取组成员位置的方法**
    bool get_group_member_locations(int group_id, vector<VOXEL_LOC>& member_locations) const {
        auto it = group_controllers.find(group_id);
        if(it == group_controllers.end() || !it->second.is_active) {
            return false;
        }

        member_locations.clear();
        for(const auto& loc : it->second.active_members) {
            member_locations.push_back(loc);
        }
        return true;
    }

    // // **测试和调试接口（公开给测试使用）**
    // bool can_merge_voxels(OctoTree* voxel1, OctoTree* voxel2, const Eigen::Vector3d &radar_pos);
    Eigen::Vector3d compute_radar_facing_normal(const Eigen::Vector3d &plane_normal,
                                              const Eigen::Vector3d &plane_center,
                                              const Eigen::Vector3d &radar_pos);

    //!Testing Merge Judge - 新增：全局验证方法声明
    bool can_voxel_merge_with_group_strict(OctoTree* new_voxel,
                                          const MergeGroupController& group,
                                          const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                          const Eigen::Vector3d& radar_pos);
    bool can_groups_merge_strict(const MergeGroupController& group1,
                                const MergeGroupController& group2,
                                const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                const Eigen::Vector3d& radar_pos);

    // **🔧 修复：约束去重机制 - 确保每个组只贡献一次约束**
    static bool should_contribute_merge_constraint(int group_id) {
        std::lock_guard<std::mutex> lock(constraint_mutex);
        // 检查是否已经贡献过约束
        bool already_contributed = (contributed_groups_this_ba.find(group_id) != contributed_groups_this_ba.end());
        if(!already_contributed) {
            contributed_groups_this_ba.insert(group_id);
            return true;  // 第一次贡献约束
        }
        return false;  // 已经贡献过约束，跳过
    }

    // **🔧 新增：重置约束去重标志**
    static void reset_constraint_flags() {
        std::lock_guard<std::mutex> lock(constraint_mutex);
        contributed_groups_this_ba.clear();
    }

    // **核心新增：收集合并组的完整pcrs数据**
    bool collect_merged_group_pcrs_data(int group_id,
                                       const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                       vector<PointCluster>& merged_pcrs,
                                       PointCluster& merged_pcr_fix,
                                       int wdsize);

    // **🚀 新增：预计算合并组数据**
    bool precompute_group_pcrs_data(int group_id,
                                   const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                   PrecomputedMergeData& merge_data);

    // **🚀 新增：获取预计算数据**
    bool get_precomputed_merge_data(int group_id, PrecomputedMergeData& merge_data) const {
        auto it = precomputed_merge_data.find(group_id);
        if(it != precomputed_merge_data.end() && it->second.is_valid) {
            merge_data = it->second;
            return true;
        }
        return false;
    }

    // **新增：验证方法（修复编译错误）**
    void validate_merge_group_consistency(int group_id) {
        // 简单的一致性检查
        auto it = group_controllers.find(group_id);
        if(it != group_controllers.end()) {
            // 检查组是否有效
            if(it->second.active_members.size() < 2) {
                it->second.is_active = false;
            }
        }
    }

    // **🔧 关键修复：BA前的完整验证和清理流程**
    void validate_overall_consistency(const unordered_map<VOXEL_LOC, OctoTree*>& surf_map) {
        ROS_DEBUG("Starting overall consistency validation for %zu groups", group_controllers.size());

        vector<int> groups_to_remove;
        vector<VOXEL_LOC> voxels_to_unmap;

        // 遍历所有活跃组，验证一致性
        for(auto& pair : group_controllers) {
            if(!pair.second.is_active) continue;

            int group_id = pair.first;
            auto& controller = pair.second;

            // 记录刷新前的成员
            unordered_set<VOXEL_LOC> old_members = controller.active_members;

            // 刷新参数（会自动移除失效成员）
            controller.refresh_parameters_if_needed(surf_map);

            // 检查组是否仍然有效
            if(!controller.is_valid_for_optimization()) {
                groups_to_remove.push_back(group_id);
                ROS_DEBUG("Group %d marked for removal: insufficient valid members", group_id);
                continue;
            }

            // 找出被移除的体素
            for(const auto& old_loc : old_members) {
                if(controller.active_members.find(old_loc) == controller.active_members.end()) {
                    voxels_to_unmap.push_back(old_loc);
                    ROS_DEBUG("Voxel [%ld,%ld,%ld] removed from group %d",
                             old_loc.x, old_loc.y, old_loc.z, group_id);
                }
            }
        }

        // 清理被移除体素的映射关系
        for(const auto& loc : voxels_to_unmap) {
            auto voxel_it = surf_map.find(loc);
            if(voxel_it != surf_map.end() && voxel_it->second) {
                voxel_it->second->is_merged = false;
                voxel_it->second->merge_group_id = -1;
            }
            voxel_to_group.erase(loc);
        }

        // 移除完全失效的组
        for(int group_id : groups_to_remove) {
            auto it = group_controllers.find(group_id);
            if(it != group_controllers.end()) {
                // 重置剩余成员的合并状态
                for(const auto& loc : it->second.active_members) {
                    auto voxel_it = surf_map.find(loc);
                    if(voxel_it != surf_map.end() && voxel_it->second) {
                        voxel_it->second->is_merged = false;
                        voxel_it->second->merge_group_id = -1;
                    }
                    voxel_to_group.erase(loc);
                }
                group_controllers.erase(it);
            }
        }

        ROS_DEBUG("Consistency validation complete: removed %zu groups, %zu voxel mappings",
                 groups_to_remove.size(), voxels_to_unmap.size());
    }

private:
    // **🚀 性能优化：邻域查找缓存**
    struct NeighborCache {
        unordered_map<VOXEL_LOC, vector<VOXEL_LOC>> cached_neighbors;
        size_t max_cache_size = 1000;  // 限制缓存大小防止内存过度使用

        void clear() { cached_neighbors.clear(); }

        bool get_cached_neighbors(const VOXEL_LOC& center, vector<VOXEL_LOC>& neighbors) {
            auto it = cached_neighbors.find(center);
            if(it != cached_neighbors.end()) {
                neighbors = it->second;
                return true;
            }
            return false;
        }

        void cache_neighbors(const VOXEL_LOC& center, const vector<VOXEL_LOC>& neighbors) {
            if(cached_neighbors.size() >= max_cache_size) {
                // 简单的LRU：清空一半缓存
                auto it = cached_neighbors.begin();
                std::advance(it, cached_neighbors.size() / 2);
                cached_neighbors.erase(cached_neighbors.begin(), it);
            }
            cached_neighbors[center] = neighbors;
        }
    } neighbor_cache;

    // **🚀 第三步优化：增量空间索引结构**
    struct IncrementalSpatialIndex {
        // 空间哈希桶，每个桶包含该区域内的体素
        unordered_map<int64_t, unordered_set<VOXEL_LOC>> spatial_buckets;
        unordered_set<VOXEL_LOC> indexed_voxels;  // 跟踪已索引的体素
        static constexpr int BUCKET_SIZE = 8;  // 每个桶覆盖8x8x8的体素区域

        void clear() {
            spatial_buckets.clear();
            indexed_voxels.clear();
        }

        // 计算体素的空间哈希值
        int64_t compute_spatial_hash(const VOXEL_LOC& loc) const {
            int64_t bx = loc.x / BUCKET_SIZE;
            int64_t by = loc.y / BUCKET_SIZE;
            int64_t bz = loc.z / BUCKET_SIZE;
            // 使用简单的哈希函数组合三个坐标
            return bx + by * 1000000LL + bz * 1000000000000LL;
        }

        // **🚀 新增：增量添加体素**
        void add_voxel(const VOXEL_LOC& loc) {
            if(indexed_voxels.find(loc) != indexed_voxels.end()) {
                return;  // 已经存在，跳过
            }

            int64_t hash = compute_spatial_hash(loc);
            spatial_buckets[hash].insert(loc);
            indexed_voxels.insert(loc);
        }

        // **🚀 新增：增量移除体素**
        void remove_voxel(const VOXEL_LOC& loc) {
            if(indexed_voxels.find(loc) == indexed_voxels.end()) {
                return;  // 不存在，跳过
            }

            int64_t hash = compute_spatial_hash(loc);
            auto bucket_it = spatial_buckets.find(hash);
            if(bucket_it != spatial_buckets.end()) {
                bucket_it->second.erase(loc);
                if(bucket_it->second.empty()) {
                    spatial_buckets.erase(bucket_it);  // 清理空桶
                }
            }
            indexed_voxels.erase(loc);
        }

        // 获取指定区域内的所有体素
        void get_voxels_in_region(const VOXEL_LOC& center, int radius, vector<VOXEL_LOC>& result) const {
            result.clear();

            int64_t center_bx = center.x / BUCKET_SIZE;
            int64_t center_by = center.y / BUCKET_SIZE;
            int64_t center_bz = center.z / BUCKET_SIZE;

            int bucket_radius = (radius + BUCKET_SIZE - 1) / BUCKET_SIZE;  // 向上取整

            for(int64_t dx = -bucket_radius; dx <= bucket_radius; dx++) {
                for(int64_t dy = -bucket_radius; dy <= bucket_radius; dy++) {
                    for(int64_t dz = -bucket_radius; dz <= bucket_radius; dz++) {
                        int64_t bx = center_bx + dx;
                        int64_t by = center_by + dy;
                        int64_t bz = center_bz + dz;
                        int64_t hash = bx + by * 1000000LL + bz * 1000000000000LL;

                        auto it = spatial_buckets.find(hash);
                        if(it != spatial_buckets.end()) {
                            for(const auto& loc : it->second) {
                                // 精确距离检查
                                int64_t dx_exact = abs(loc.x - center.x);
                                int64_t dy_exact = abs(loc.y - center.y);
                                int64_t dz_exact = abs(loc.z - center.z);
                                if(dx_exact <= radius && dy_exact <= radius && dz_exact <= radius) {
                                    result.push_back(loc);
                                }
                            }
                        }
                    }
                }
            }
        }

        // **🚀 新增：批量更新索引（增量版本）**
        void update_from_surf_map_incremental(const unordered_map<VOXEL_LOC, OctoTree*> &surf_map) {
            // 找出需要添加和移除的体素
            unordered_set<VOXEL_LOC> current_valid_voxels;

            for(const auto& pair : surf_map) {
                if(pair.second && pair.second->octo_state == 0 && pair.second->plane.is_plane) {
                    current_valid_voxels.insert(pair.first);
                }
            }

            // 移除不再有效的体素
            for(auto it = indexed_voxels.begin(); it != indexed_voxels.end();) {
                if(current_valid_voxels.find(*it) == current_valid_voxels.end()) {
                    VOXEL_LOC to_remove = *it;
                    it = indexed_voxels.erase(it);

                    // 从空间桶中移除
                    int64_t hash = compute_spatial_hash(to_remove);
                    auto bucket_it = spatial_buckets.find(hash);
                    if(bucket_it != spatial_buckets.end()) {
                        bucket_it->second.erase(to_remove);
                        if(bucket_it->second.empty()) {
                            spatial_buckets.erase(bucket_it);
                        }
                    }
                } else {
                    ++it;
                }
            }

            // 添加新的有效体素
            for(const auto& loc : current_valid_voxels) {
                add_voxel(loc);
            }
        }
    } spatial_index;

    // **🚀 第四步优化：平面参数缓存（安全版本）**
    struct PlaneParameterCache {
        struct CachedPlaneParams {
            Eigen::Vector3d radar_facing_normal;
            Eigen::Vector3d center;
            Eigen::Matrix<double, 6, 6> plane_cov;
            bool is_valid;

            CachedPlaneParams() : is_valid(false) {}
        };

        // **🔧 安全修复：使用VOXEL_LOC作为键而不是指针**
        unordered_map<VOXEL_LOC, CachedPlaneParams> cached_params;
        Eigen::Vector3d last_radar_pos;
        bool radar_pos_changed = true;
        size_t max_cache_size = 500;  // 限制缓存大小

        void clear() {
            cached_params.clear();
            radar_pos_changed = true;
        }

        void update_radar_position(const Eigen::Vector3d& new_radar_pos) {
            if((new_radar_pos - last_radar_pos).norm() > 0.1) {  // 雷达位置变化超过10cm
                cached_params.clear();  // 清空缓存，因为法向量方向可能改变
                radar_pos_changed = true;
            }
            last_radar_pos = new_radar_pos;
        }

        bool get_cached_params(const VOXEL_LOC& loc, CachedPlaneParams& params) {
            if(radar_pos_changed) return false;  // 雷达位置变化，缓存无效

            auto it = cached_params.find(loc);
            if(it != cached_params.end() && it->second.is_valid) {
                params = it->second;
                return true;
            }
            return false;
        }

        void cache_params(const VOXEL_LOC& loc, const CachedPlaneParams& params) {
            if(cached_params.size() >= max_cache_size) {
                // 简单的清理策略：清空一半缓存
                auto it = cached_params.begin();
                std::advance(it, cached_params.size() / 2);
                cached_params.erase(cached_params.begin(), it);
            }
            cached_params[loc] = params;
        }
    } plane_param_cache;

    // **🚀 第五步优化：增量更新策略**
    struct IncrementalUpdateTracker {
        unordered_set<VOXEL_LOC> processed_this_frame;
        unordered_set<VOXEL_LOC> changed_voxels;
        unordered_set<VOXEL_LOC> new_voxels;
        int frame_count = 0;
        static constexpr int FULL_UPDATE_INTERVAL = 10;  // 每10帧进行一次全量更新

        void clear() {
            processed_this_frame.clear();
            changed_voxels.clear();
            new_voxels.clear();
        }

        void start_new_frame() {
            frame_count++;
            processed_this_frame.clear();
        }

        bool should_process_voxel(const VOXEL_LOC& loc) {
            // 如果是全量更新帧，处理所有体素
            if(frame_count % FULL_UPDATE_INTERVAL == 0) {
                return true;
            }

            // 增量更新：只处理变化的体素
            return changed_voxels.find(loc) != changed_voxels.end() ||
                   new_voxels.find(loc) != new_voxels.end();
        }

        void mark_voxel_processed(const VOXEL_LOC& loc) {
            processed_this_frame.insert(loc);
        }

        void mark_voxel_changed(const VOXEL_LOC& loc) {
            changed_voxels.insert(loc);
        }

        void mark_voxel_new(const VOXEL_LOC& loc) {
            new_voxels.insert(loc);
        }

        bool is_full_update_frame() const {
            return frame_count % FULL_UPDATE_INTERVAL == 0;
        }

        size_t get_candidates_count() const {
            if(is_full_update_frame()) {
                return SIZE_MAX;  // 表示全量更新
            }
            return changed_voxels.size() + new_voxels.size();
        }
    } incremental_tracker;

    // **🚀 第四步优化：性能统计**
    struct PerformanceStats {
        int cache_hits = 0;
        int cache_misses = 0;
        int spatial_index_queries = 0;
        int neighbor_checks = 0;
        int merge_attempts = 0;
        std::chrono::high_resolution_clock::time_point start_time;

        void reset() {
            cache_hits = cache_misses = 0;
            spatial_index_queries = neighbor_checks = merge_attempts = 0;
            start_time = std::chrono::high_resolution_clock::now();
        }

        void print_stats() const {
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);

            ROS_DEBUG("=== Voxel Merge Performance Stats ===");
            ROS_DEBUG("Processing time: %ld μs", duration.count());
            ROS_DEBUG("Param cache hits/misses: %d/%d (%.1f%% hit rate)",
                     cache_hits, cache_misses,
                     cache_hits + cache_misses > 0 ? 100.0 * cache_hits / (cache_hits + cache_misses) : 0.0);
            ROS_DEBUG("Spatial queries: %d, Neighbor checks: %d, Merge attempts: %d",
                     spatial_index_queries, neighbor_checks, merge_attempts);
        }
    } perf_stats;

    // **🚀 新增：合并决策缓存系统**
    struct MergeDecisionCache {
        struct CacheKey {
            VOXEL_LOC loc1, loc2;

            bool operator==(const CacheKey& other) const {
                return (loc1 == other.loc1 && loc2 == other.loc2) ||
                       (loc1 == other.loc2 && loc2 == other.loc1);  // 对称性
            }
        };

        struct CacheKeyHash {
            size_t operator()(const CacheKey& key) const {
                // 确保对称性：总是让较小的loc在前面
                VOXEL_LOC first = key.loc1, second = key.loc2;
                if(first.x > second.x || (first.x == second.x && first.y > second.y) ||
                   (first.x == second.x && first.y == second.y && first.z > second.z)) {
                    std::swap(first, second);
                }

                size_t h1 = std::hash<int64_t>{}(first.x);
                size_t h2 = std::hash<int64_t>{}(first.y);
                size_t h3 = std::hash<int64_t>{}(first.z);
                size_t h4 = std::hash<int64_t>{}(second.x);
                size_t h5 = std::hash<int64_t>{}(second.y);
                size_t h6 = std::hash<int64_t>{}(second.z);

                return h1 ^ (h2 << 1) ^ (h3 << 2) ^ (h4 << 3) ^ (h5 << 4) ^ (h6 << 5);
            }
        };

        unordered_map<CacheKey, bool, CacheKeyHash> cached_decisions;
        size_t max_cache_size = 1000;  // 限制缓存大小
        int cache_hits = 0;
        int cache_misses = 0;

        void clear() {
            cached_decisions.clear();
            cache_hits = cache_misses = 0;
        }

        bool get_cached_decision(const VOXEL_LOC& loc1, const VOXEL_LOC& loc2, bool& found) {
            CacheKey key{loc1, loc2};
            auto it = cached_decisions.find(key);
            if(it != cached_decisions.end()) {
                found = true;
                cache_hits++;
                return it->second;
            }
            found = false;
            cache_misses++;
            return false;
        }

        void cache_decision(const VOXEL_LOC& loc1, const VOXEL_LOC& loc2, bool decision) {
            if(cached_decisions.size() >= max_cache_size) {
                // 简单的清理策略：清空一半缓存
                auto it = cached_decisions.begin();
                std::advance(it, cached_decisions.size() / 2);
                cached_decisions.erase(cached_decisions.begin(), it);
            }

            CacheKey key{loc1, loc2};
            cached_decisions[key] = decision;
        }

        double get_hit_rate() const {
            int total = cache_hits + cache_misses;
            return total > 0 ? (double)cache_hits / total : 0.0;
        }
    } merge_decision_cache;

    // **核心算法**
    void process_merge_candidates(unordered_map<VOXEL_LOC, OctoTree*> &surf_map,
                                 const Eigen::Vector3d &radar_pos);
    void merge_voxel_pair(const VOXEL_LOC &loc1, const VOXEL_LOC &loc2,
                         OctoTree* voxel1, OctoTree* voxel2);
    vector<VOXEL_LOC> get_adjacent_voxels(const VOXEL_LOC &center);

    // **新增：跨层级邻居搜索**
    vector<VOXEL_LOC> find_multi_level_neighbors(const VOXEL_LOC &center, int center_layer,
                                                 const unordered_map<VOXEL_LOC, OctoTree*> &surf_map);

    // **🚀 第三步优化：使用空间索引的快速邻域搜索**
    vector<VOXEL_LOC> find_neighbors_with_spatial_index(const VOXEL_LOC &center,
                                                        const unordered_map<VOXEL_LOC, OctoTree*> &surf_map);

    // **🚀 第四步优化：安全的平面参数获取**
    bool get_plane_params_safe(OctoTree* voxel, const Eigen::Vector3d& radar_pos,
                              Eigen::Vector3d& radar_facing_normal,
                              Eigen::Vector3d& center,
                              Eigen::Matrix<double, 6, 6>& plane_cov);

    // **🚀 新增：优化的马氏距离计算（保留不确定性，提升性能）**
    bool can_merge_voxels_optimized_mahalanobis(OctoTree* voxel1, OctoTree* voxel2, const Eigen::Vector3d &radar_pos);

    // **备用：使用缓存的平面参数获取（安全版本）**
    bool get_cached_plane_params(const VOXEL_LOC& loc, OctoTree* voxel, const Eigen::Vector3d& radar_pos,
                                Eigen::Vector3d& radar_facing_normal,
                                Eigen::Vector3d& center,
                                Eigen::Matrix<double, 6, 6>& plane_cov);
};

// 静态实例指针
VoxelMergeManager* VoxelMergeManager::instance_ = nullptr;

// **静态成员变量定义**
unordered_set<int> VoxelMergeManager::contributed_groups_this_ba;
std::mutex VoxelMergeManager::constraint_mutex;

// **实现：核心合并处理**
void VoxelMergeManager::perform_voxel_merging(unordered_map<VOXEL_LOC, OctoTree*> &surf_map,
                                            const Eigen::Vector3d &radar_pos) {
    if(surf_map.empty()) return;

    size_t candidates_count = update_tracker.get_total_candidates();
    if(candidates_count == 0) return;

    process_merge_candidates(surf_map, radar_pos);
    update_tracker.clear_tracking_data();
}

void VoxelMergeManager::process_merge_candidates(unordered_map<VOXEL_LOC, OctoTree*> &surf_map,   // 全局体素地图
                                               const Eigen::Vector3d &radar_pos) {    // 雷达位置（用于法向量方向判断）
    // **🚀 第四步优化：启动性能统计**
    perf_stats.reset();

    auto candidates = update_tracker.get_merge_candidates();

    //!Testing Merge Judge - 存储surf_map引用供全局验证使用
    current_surf_map = &surf_map;
    current_radar_pos = radar_pos;

    // **🚀 第三步优化：增量更新空间索引**
    spatial_index.update_from_surf_map_incremental(surf_map);

    // **🚀 第二步优化：批量预处理有效体素（添加OpenMP并行化）**
    struct ValidVoxel {
        VOXEL_LOC location;
        OctoTree* octree;
        int layer;
    };

    vector<ValidVoxel> valid_voxels;
    valid_voxels.reserve(candidates.size());  // 预分配空间

    // 批量过滤有效候选体素
    for(const auto &loc : candidates) {
        auto it = surf_map.find(loc);
        if(it == surf_map.end() || !it->second) continue;   // 体素不存在检查

        OctoTree* octree = it->second;
        if(octree->octo_state != 0 || !octree->plane.is_plane) continue;  // 状态和平面有效性检查

        valid_voxels.push_back({loc, octree, octree->layer});
    }

    //!Testing Merge Judge - 统计验证结果
    int total_merge_attempts = 0;
    int rejected_by_strict_validation = 0;

    // **🚀 第四步优化：智能邻域搜索和处理（添加OpenMP并行化）**
    // 使用OpenMP并行化体素合并处理
    #pragma omp parallel for schedule(dynamic) if(valid_voxels.size() > 100)
    for(size_t i = 0; i < valid_voxels.size(); i++) {
        const auto& voxel = valid_voxels[i];

        // **使用空间索引进行快速邻域搜索**
        vector<VOXEL_LOC> all_neighbors = find_neighbors_with_spatial_index(voxel.location, surf_map);
        #pragma omp atomic
        perf_stats.spatial_index_queries++;

        // **🚀 新增：邻域预过滤，避免重复的合并检查**
        for(const auto &neighbor_loc : all_neighbors) {
            #pragma omp atomic
            perf_stats.neighbor_checks++;
            // 避免重复处理：只处理索引更大的邻居（确保每对体素只检查一次）
            bool should_skip = false;
            for(size_t j = i + 1; j < valid_voxels.size(); j++) {
                if(valid_voxels[j].location == neighbor_loc) {
                    should_skip = true;  // 这个邻居会在后续循环中作为主体素处理
                    break;
                }
            }
            if(should_skip) continue;

            // 由于find_neighbors_with_spatial_index已经过滤了有效性，这里可以直接使用
            auto neighbor_it = surf_map.find(neighbor_loc);
            if(neighbor_it == surf_map.end() || !neighbor_it->second) continue;  // 额外安全检查

            OctoTree* neighbor_octree = neighbor_it->second;

            // **🚀 性能优化：使用缓存的合并决策**
            bool found;
            bool can_merge = merge_decision_cache.get_cached_decision(voxel.location, neighbor_loc, found);

            if(!found) {
                // 缓存未命中，执行实际计算
                can_merge = can_merge_voxels_optimized_mahalanobis(voxel.octree, neighbor_octree, radar_pos);
                merge_decision_cache.cache_decision(voxel.location, neighbor_loc, can_merge);
                #pragma omp atomic
                perf_stats.cache_misses++;
            } else {
                #pragma omp atomic
                perf_stats.cache_hits++;
            }

            if(can_merge) {
                //!Testing Merge Judge - 统计合并尝试
                #pragma omp atomic
                total_merge_attempts++;

                // **🔧 关键修复：在并行环境中需要同步合并操作**
                #pragma omp critical(merge_operation)
                {
                    // 记录合并前的状态用于统计
                    auto it1 = voxel_to_group.find(voxel.location);
                    auto it2 = voxel_to_group.find(neighbor_loc);
                    int group1 = (it1 != voxel_to_group.end()) ? it1->second : -1;
                    int group2 = (it2 != voxel_to_group.end()) ? it2->second : -1;

                    size_t groups_before = group_controllers.size();
                    merge_voxel_pair(voxel.location, neighbor_loc, voxel.octree, neighbor_octree);
                    size_t groups_after = group_controllers.size();

                    // 如果组数量没有变化且不是同组合并，说明被严格验证拒绝了
                    if(groups_before == groups_after && group1 != group2 && !(group1 == -1 && group2 == -1)) {
                        rejected_by_strict_validation++;
                    }
                }
            }
        }
    }

    // **🚀 第四步优化：输出性能统计**
    perf_stats.merge_attempts = total_merge_attempts;
    perf_stats.print_stats();

    // **🚀 新增：输出合并决策缓存统计**
    ROS_DEBUG("Merge decision cache: %.1f%% hit rate (%d hits, %d misses)",
             merge_decision_cache.get_hit_rate() * 100.0,
             merge_decision_cache.cache_hits,
             merge_decision_cache.cache_misses);

    // //!Testing Merge Judge - 输出严格验证统计信息
    // if(ENABLE_STRICT_GROUP_VALIDATION && total_merge_attempts > 0) {
    //     ROS_INFO("STRICT_VALIDATION: Attempts=%d Rejected=%d Success=%d Rate=%.1f%%",
    //              total_merge_attempts, rejected_by_strict_validation,
    //              total_merge_attempts - rejected_by_strict_validation,
    //              100.0 * (total_merge_attempts - rejected_by_strict_validation) / total_merge_attempts);
    // }
}

// **实现：体素合并判断（修复关键问题）**
void VoxelMergeManager::merge_voxel_pair(const VOXEL_LOC &loc1, const VOXEL_LOC &loc2,
                                        OctoTree* voxel1, OctoTree* voxel2) {
    auto it1 = voxel_to_group.find(loc1);
    auto it2 = voxel_to_group.find(loc2);
    int group1 = (it1 != voxel_to_group.end()) ? it1->second : -1;
    int group2 = (it2 != voxel_to_group.end()) ? it2->second : -1;

    if(group1 == group2 && group1 != -1) return; // 已在同一组

    int target_group_id;

    if(group1 == -1 && group2 == -1) {
        //!Testing Merge Judge - 场景1：两个体素都未合并，创建新组
        // 这种情况下两个体素已经通过can_merge_voxels验证，可以直接合并
        target_group_id = next_group_id++;
        MergeGroupController controller(target_group_id);
        controller.add_member(loc1, voxel1);
        controller.add_member(loc2, voxel2);

        group_controllers.emplace(target_group_id, std::move(controller));
        voxel_to_group[loc1] = target_group_id;
        voxel_to_group[loc2] = target_group_id;

        //!Testing Merge Judge - 修复：不在合并时设置状态，等待BA前统一设置
        // 注释掉：体素状态会在cleanup和recut后发生变化，在这里设置会被覆盖
        // voxel1->is_merged = true;
        // voxel1->merge_group_id = target_group_id;
        // voxel2->is_merged = true;
        // voxel2->merge_group_id = target_group_id;

        ROS_DEBUG("Created new merge group %d with voxels [%ld,%ld,%ld] and [%ld,%ld,%ld]",
                 target_group_id, loc1.x, loc1.y, loc1.z, loc2.x, loc2.y, loc2.z);
    }
    else if(group1 != -1 && group2 == -1) {
        //!Testing Merge Judge - 场景2：voxel2要加入已有的group1，需要全局验证
        if(ENABLE_STRICT_GROUP_VALIDATION && current_surf_map != nullptr) {
            // 严格验证：voxel2必须与group1中的每个成员都满足合并条件
            if(!can_voxel_merge_with_group_strict(voxel2, group_controllers[group1],
                                                 *current_surf_map, current_radar_pos)) {
                ROS_DEBUG("Merge rejected: voxel2 [%ld,%ld,%ld] incompatible with group %d members",
                         loc2.x, loc2.y, loc2.z, group1);
                return;  // 拒绝合并，防止链式漂移
            }
        }

        // 验证通过或未启用严格验证，执行合并
        target_group_id = group1;
        group_controllers[group1].add_member(loc2, voxel2);
        voxel_to_group[loc2] = group1;

        //!Testing Merge Judge - 修复：不在合并时设置状态，等待BA前统一设置
        // 注释掉：体素状态会在cleanup和recut后发生变化，在这里设置会被覆盖
        // voxel2->is_merged = true;
        // voxel2->merge_group_id = group1;

        ROS_DEBUG("Added voxel [%ld,%ld,%ld] to existing group %d",
                 loc2.x, loc2.y, loc2.z, group1);
    }
    else if(group1 == -1 && group2 != -1) {
        //!Testing Merge Judge - 场景3：voxel1要加入已有的group2，需要全局验证
        if(ENABLE_STRICT_GROUP_VALIDATION && current_surf_map != nullptr) {
            // 严格验证：voxel1必须与group2中的每个成员都满足合并条件
            if(!can_voxel_merge_with_group_strict(voxel1, group_controllers[group2],
                                                 *current_surf_map, current_radar_pos)) {
                ROS_DEBUG("Merge rejected: voxel1 [%ld,%ld,%ld] incompatible with group %d members",
                         loc1.x, loc1.y, loc1.z, group2);
                return;  // 拒绝合并，防止链式漂移
            }
        }

        // 验证通过或未启用严格验证，执行合并
        target_group_id = group2;
        group_controllers[group2].add_member(loc1, voxel1);
        voxel_to_group[loc1] = group2;

        //!Testing Merge Judge - 修复：不在合并时设置状态，等待BA前统一设置
        // 注释掉：体素状态会在cleanup和recut后发生变化，在这里设置会被覆盖
        // voxel1->is_merged = true;
        // voxel1->merge_group_id = group2;

        ROS_DEBUG("Added voxel [%ld,%ld,%ld] to existing group %d",
                 loc1.x, loc1.y, loc1.z, group2);
    }
    else {
        //!Testing Merge Judge - 场景4：合并两个已有组，需要组间全局验证
        if(ENABLE_STRICT_GROUP_VALIDATION && current_surf_map != nullptr) {
            // 严格验证：两个组的所有成员之间都必须满足合并条件
            if(!can_groups_merge_strict(group_controllers[group1], group_controllers[group2],
                                       *current_surf_map, current_radar_pos)) {
                ROS_DEBUG("Group merge rejected: group %d and group %d have incompatible members",
                         group1, group2);
                return;  // 拒绝组合并，防止链式漂移
            }
        }

        // 验证通过或未启用严格验证，执行组合并
        target_group_id = group1;
        auto& target_controller = group_controllers[group1];
        auto& source_controller = group_controllers[group2];

        // 将group2的成员移到group1
        for(const auto& loc : source_controller.active_members) {
            target_controller.active_members.insert(loc);
            voxel_to_group[loc] = group1;

            //!Testing Merge Judge - 修复：不在合并时设置状态，等待BA前统一设置
            // 注释掉：体素状态会在cleanup和recut后发生变化，在这里设置会被覆盖
            // if(current_surf_map != nullptr) {
            //     auto it = current_surf_map->find(loc);
            //     if(it != current_surf_map->end() && it->second) {
            //         it->second->merge_group_id = group1;
            //     }
            // }
        }
        target_controller.member_octrees.insert(target_controller.member_octrees.end(),
                                               source_controller.member_octrees.begin(),
                                               source_controller.member_octrees.end());
        target_controller.needs_parameter_refresh = true;

        // 删除group2
        group_controllers.erase(group2);

        ROS_DEBUG("Merged group %d into group %d", group2, group1);
    }
}

// **🚀 性能优化版：为BA优化准备（预计算合并数据）**
int VoxelMergeManager::prepare_for_optimization(unordered_map<VOXEL_LOC, OctoTree*> &surf_map) {
    //!Testing Merge Judge - 重新设计：简化职责，只负责BA前的准备工作

    // **职责1：重置帧级别的约束跟踪**
    reset_frame_constraint_tracking();

    // **职责2：刷新所有活跃组的参数（确保参数是最新的）**
    for(auto& pair : group_controllers) {
        if(pair.second.is_active) {
            pair.second.refresh_parameters_if_needed(surf_map);
        }
    }

    // **🚀 性能优化：智能管理预计算缓存 - 只清理失效组的数据**
    // 不要全部清空，只清理无效组的预计算数据
    for(auto it = precomputed_merge_data.begin(); it != precomputed_merge_data.end();) {
        if(group_controllers.find(it->first) == group_controllers.end() ||
           !group_controllers[it->first].is_active) {
            it = precomputed_merge_data.erase(it);
        } else {
            ++it;
        }
    }

    // **职责3：在所有状态变化完成后，最终设置体素合并状态**
    //!Testing Merge Judge - 关键修复：在cleanup和recut完成后才设置体素状态

    // 首先清空所有体素的合并状态（防止历史遗留）
    for(auto& pair : surf_map) {
        if(pair.second) {
            pair.second->is_merged = false;
            pair.second->merge_group_id = -1;
        }
    }

    // 然后为有效组的有效体素设置合并状态
    int merged_voxels_count = 0;
    int active_groups = 0;
    int total_group_members = 0;

    for(auto& controller_pair : group_controllers) {
        auto& controller = controller_pair.second;
        if(!controller.is_valid_for_optimization()) continue;

        active_groups++;

        // **🚀 性能优化：预计算合并组的pcrs数据**
        PrecomputedMergeData merge_data;
        bool precompute_success = precompute_group_pcrs_data(controller.group_id, surf_map, merge_data);

        // 为这个组的所有有效成员设置合并状态
        for(const auto& loc : controller.active_members) {
            auto it = surf_map.find(loc);
            if(it != surf_map.end() && it->second && it->second->isexist && it->second->octo_state == 0) {
                // 只有叶子节点且存在的体素才能被标记为合并
                it->second->is_merged = true;
                it->second->merge_group_id = controller.group_id;
                it->second->merged_plane = controller.merged_plane;
                it->second->merged_eig_value = controller.merged_eig_value;
                it->second->merged_eig_vector = controller.merged_eig_vector;
                it->second->merged_pcr_add = controller.merged_pcr_add;

                // **🚀 性能优化：设置预计算数据可用标志**
                it->second->precomputed_data_available = precompute_success;

                merged_voxels_count++;
                total_group_members++;
            }
        }

        // **🚀 性能优化：缓存预计算的数据**
        if(precompute_success) {
            precomputed_merge_data[controller.group_id] = merge_data;
        }
    }

    //!Testing Merge Judge - 新增：验证统计一致性
    int verification_merged_count = 0;
    for(const auto& pair : surf_map) {
        if(pair.second && pair.second->isexist && pair.second->is_merged) {
            verification_merged_count++;
        }
    }

    // ROS_INFO("=== BA Optimization Parameter Statistics ===");
    // ROS_INFO("Active merge groups: %d", active_groups);
    // ROS_INFO("Total merged voxels: %d", total_group_members);
    // ROS_INFO("Actually set merged voxels: %d", merged_voxels_count);
    // ROS_INFO("Verification count (surf_map): %d", verification_merged_count);

    //!Testing Merge Judge - 警告：如果统计不一致
    if(merged_voxels_count != verification_merged_count) {
        ROS_WARN("INCONSISTENCY: Actually_set=%d vs Verification=%d (diff=%d)",
                 merged_voxels_count, verification_merged_count,
                 verification_merged_count - merged_voxels_count);
    }

    return merged_voxels_count;
}

// **实现：清理无效组**
void VoxelMergeManager::cleanup_invalid_groups(unordered_map<VOXEL_LOC, OctoTree*> &surf_map) {
    vector<int> invalid_groups;

    for(auto& pair : group_controllers) {
        auto& controller = pair.second;

        // **🔧 保守的成员有效性检查：只检查基本存在性**
        unordered_set<VOXEL_LOC> valid_members;
        for(const auto& loc : controller.active_members) {
            auto it = surf_map.find(loc);
            // **保守策略：只要体素对象存在就认为有效**
            // 避免因为临时状态变化（如recut、边缘化）导致组被错误删除
            if(it != surf_map.end() && it->second) {
                valid_members.insert(loc);
            }
        }

        // **关键修复：如果成员有变化，清理指针缓存**
        if(valid_members.size() != controller.active_members.size()) {
            controller.member_octrees.clear();
            controller.needs_parameter_refresh = true;
        }

        controller.active_members = valid_members;
        if(valid_members.size() < 2) {
            invalid_groups.push_back(pair.first);
        }
    }

    // **🔧 关键修复：移除无效组并重置体素合并状态**
    for(int group_id : invalid_groups) {
        auto it = group_controllers.find(group_id);
        if(it != group_controllers.end()) {
            // **步骤1：重置所有成员体素的合并状态**
            for(const auto& loc : it->second.active_members) {
                auto voxel_it = surf_map.find(loc);
                if(voxel_it != surf_map.end() && voxel_it->second) {
                    OctoTree* voxel = voxel_it->second;
                    // **关键修复：清除体素的合并状态**
                    voxel->is_merged = false;
                    voxel->merge_group_id = -1;
                    // 保留merged_plane等参数，因为它们可能仍然有效
                }
                // **步骤2：清除映射关系**
                voxel_to_group.erase(loc);
            }
            // **步骤3：删除组控制器**
            group_controllers.erase(it);
        }
    }

    // **🔍 输出清理统计信息**
    if(!invalid_groups.empty()) {
        ROS_DEBUG("Cleanup: removed %zu invalid merge groups and reset voxel merge states",
                  invalid_groups.size());
    }
}

// **实现：处理体素删除**
void VoxelMergeManager::handle_voxel_removal(const VOXEL_LOC &loc) {
    auto it = voxel_to_group.find(loc);
    if(it != voxel_to_group.end()) {
        int group_id = it->second;
        auto group_it = group_controllers.find(group_id);
        if(group_it != group_controllers.end()) {
            group_it->second.remove_member(loc);
        }
        voxel_to_group.erase(it);
    }
}

void VoxelMergeManager::handle_multiple_voxel_removals(const vector<VOXEL_LOC> &locs) {
    for(const auto& loc : locs) {
        handle_voxel_removal(loc);
    }
}

// **实现：约束传递控制**
bool VoxelMergeManager::should_contribute_constraint(int group_id) {
    if(processed_groups_this_frame.find(group_id) == processed_groups_this_frame.end()) {
        processed_groups_this_frame.insert(group_id);
        return true;  // 第一次遇到这个组，应该传递约束
    }
    return false;  // 已经处理过，跳过
}

// **实现：清理合并数据**
void VoxelMergeManager::clear_merge_data() {
    group_controllers.clear();
    voxel_to_group.clear();
    next_group_id = 1;
    update_tracker.clear_tracking_data();
    processed_groups_this_frame.clear();

    // **🚀 性能优化：清理所有缓存和索引**
    neighbor_cache.clear();
    spatial_index.clear();
    plane_param_cache.clear();
    incremental_tracker.clear();
    merge_decision_cache.clear();
}

// **🚀 优化版：使用缓存的邻域查找**
vector<VOXEL_LOC> VoxelMergeManager::get_adjacent_voxels(const VOXEL_LOC &center) {
    vector<VOXEL_LOC> neighbors;

    // 首先尝试从缓存获取
    if(neighbor_cache.get_cached_neighbors(center, neighbors)) {
        return neighbors;  // 缓存命中，直接返回
    }

    // 缓存未命中，计算邻域
    neighbors.reserve(6);  // 预分配空间
    int offsets[6][3] = {{1,0,0}, {-1,0,0}, {0,1,0}, {0,-1,0}, {0,0,1}, {0,0,-1}};
    for(int i = 0; i < 6; i++) {
        neighbors.emplace_back(center.x + offsets[i][0],
                              center.y + offsets[i][1],
                              center.z + offsets[i][2]);
    }

    // 将结果缓存
    neighbor_cache.cache_neighbors(center, neighbors);

    return neighbors;
}

Eigen::Vector3d VoxelMergeManager::compute_radar_facing_normal(const Eigen::Vector3d &plane_normal,
                                                              const Eigen::Vector3d &plane_center,
                                                              const Eigen::Vector3d &radar_pos) {
    Eigen::Vector3d to_radar = radar_pos - plane_center;
    Eigen::Vector3d normal = plane_normal;
    if(normal.dot(to_radar) < 0) {
        normal = -normal;
    }
    return normal;
}

// **新增：跨层级邻居搜索**
vector<VOXEL_LOC> VoxelMergeManager::find_multi_level_neighbors(const VOXEL_LOC &center, int center_layer,
                                                 const unordered_map<VOXEL_LOC, OctoTree*> &surf_map) {
    vector<VOXEL_LOC> neighbors;
    vector<HierarchicalNeighborFinder::NeighborInfo> all_neighbors = HierarchicalNeighborFinder().find_all_neighbors(
        center, center_layer, surf_map
    );
    for(const auto &neighbor : all_neighbors) {
        neighbors.push_back(neighbor.location);
    }
    return neighbors;
}

// **🚀 第三步优化：使用空间索引的快速邻域搜索**
vector<VOXEL_LOC> VoxelMergeManager::find_neighbors_with_spatial_index(const VOXEL_LOC &center,
                                                        const unordered_map<VOXEL_LOC, OctoTree*> &surf_map) {
    vector<VOXEL_LOC> neighbors;

    // 使用空间索引快速获取邻域候选
    spatial_index.get_voxels_in_region(center, 2, neighbors);  // 搜索半径为2的邻域

    // 过滤出真正有效的邻居（存在且有效）
    vector<VOXEL_LOC> valid_neighbors;
    valid_neighbors.reserve(neighbors.size());

    for(const auto& neighbor_loc : neighbors) {
        // 跳过自己
        if(neighbor_loc.x == center.x && neighbor_loc.y == center.y && neighbor_loc.z == center.z) {
            continue;
        }

        // 检查是否在surf_map中存在且有效
        auto it = surf_map.find(neighbor_loc);
        if(it != surf_map.end() && it->second &&
           it->second->octo_state == 0 && it->second->plane.is_plane) {
            valid_neighbors.push_back(neighbor_loc);
        }
    }

    return valid_neighbors;
}

// **🚀 第四步优化：安全的平面参数获取**
bool VoxelMergeManager::get_plane_params_safe(OctoTree* voxel, const Eigen::Vector3d& radar_pos,
                                              Eigen::Vector3d& radar_facing_normal,
                                              Eigen::Vector3d& center,
                                              Eigen::Matrix<double, 6, 6>& plane_cov) {
    // **安全性检查**
    if(!voxel || !voxel->plane.is_plane) {
        return false;
    }

    // **直接获取参数，避免复杂的缓存逻辑**
    try {
        Eigen::Vector3d original_normal = voxel->plane.normal;
        center = voxel->plane.center;
        plane_cov = voxel->plane.plane_var;

        // **计算朝向雷达的法向量**
        radar_facing_normal = compute_radar_facing_normal(original_normal, center, radar_pos);

        // **基本有效性检查**
        if(original_normal.hasNaN() || center.hasNaN() || plane_cov.hasNaN()) {
            return false;
        }

        return true;
    } catch(...) {
        // **异常保护**
        return false;
    }
}

// **🚀 第四步优化：使用缓存的平面参数获取（安全版本）**
bool VoxelMergeManager::get_cached_plane_params(const VOXEL_LOC& loc, OctoTree* voxel, const Eigen::Vector3d& radar_pos,
                                                Eigen::Vector3d& radar_facing_normal,
                                                Eigen::Vector3d& center,
                                                Eigen::Matrix<double, 6, 6>& plane_cov) {
    if(!voxel || !voxel->plane.is_plane) return false;

    // 更新雷达位置（如果变化会清空缓存）
    plane_param_cache.update_radar_position(radar_pos);

    // 尝试从缓存获取
    PlaneParameterCache::CachedPlaneParams cached_params;
    if(plane_param_cache.get_cached_params(loc, cached_params)) {
        radar_facing_normal = cached_params.radar_facing_normal;
        center = cached_params.center;
        plane_cov = cached_params.plane_cov;
        return true;  // 缓存命中
    }

    // 缓存未命中，计算参数
    Eigen::Vector3d original_normal = voxel->plane.normal;
    center = voxel->plane.center;
    plane_cov = voxel->plane.plane_var;

    // 计算朝向雷达的法向量
    radar_facing_normal = compute_radar_facing_normal(original_normal, center, radar_pos);

    // 缓存结果
    PlaneParameterCache::CachedPlaneParams new_params;
    new_params.radar_facing_normal = radar_facing_normal;
    new_params.center = center;
    new_params.plane_cov = plane_cov;
    new_params.is_valid = true;

    plane_param_cache.cache_params(loc, new_params);

    return true;
}

// **🚀 新增：预计算合并组数据的实现**
bool VoxelMergeManager::precompute_group_pcrs_data(int group_id,
                                                  const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                                  PrecomputedMergeData& merge_data) {
    // 获取合并组的所有成员体素位置
    vector<VOXEL_LOC> member_locations;
    if(!get_group_member_locations(group_id, member_locations)) {
        return false;  // 组不存在或无效
    }

    if(member_locations.size() < 2) {
        return false;  // 成员太少
    }

    // 获取wdsize（从第一个有效体素获取）
    int wdsize = 0;
    for(const auto& loc : member_locations) {
        auto it = surf_map.find(loc);
        if(it != surf_map.end() && it->second && it->second->sw) {
            wdsize = it->second->wdsize;
            break;
        }
    }

    if(wdsize == 0) {
        return false;  // 无法确定窗口大小
    }

    // 初始化输出数据
    merge_data.merged_pcrs.resize(wdsize);
    merge_data.wdsize = wdsize;
    for(int i = 0; i < wdsize; i++) {
        merge_data.merged_pcrs[i].clear();
    }
    merge_data.merged_pcr_fix.clear();

    // 收集所有成员体素的数据
    int valid_members = 0;
    extern int* mp;  // 帧索引映射

    for(const auto& loc : member_locations) {
        auto it = surf_map.find(loc);
        if(it == surf_map.end() || !it->second) continue;

        OctoTree* voxel = it->second;
        if(!voxel->isexist || voxel->octo_state != 0) continue;

        bool collected_data = false;

        // 收集滑动窗口数据
        if(voxel->sw != nullptr) {
            for(int i = 0; i < wdsize; i++) {
                const PointCluster& frame_data = voxel->sw->pcrs_local[mp[i]];
                if(frame_data.N > 0) {
                    merge_data.merged_pcrs[i] += frame_data;
                    collected_data = true;
                }
            }
        }

        // 收集固定点数据
        if(voxel->pcr_fix.N > 0) {
            merge_data.merged_pcr_fix += voxel->pcr_fix;
            collected_data = true;
        } else if(voxel->pcr_add.N > 0) {
            merge_data.merged_pcr_fix += voxel->pcr_add;
            collected_data = true;
        }

        if(collected_data) {
            valid_members++;
        }
    }

    // 验证是否收集到足够的数据
    merge_data.is_valid = (valid_members >= 2);
    return merge_data.is_valid;
}

// **🚀 新增：优化的马氏距离计算（保留不确定性，提升性能）**
// **注释掉原有实现，参考can_merge_voxels函数重新实现**
/*
bool VoxelMergeManager::can_merge_voxels_optimized_mahalanobis(OctoTree* voxel1, OctoTree* voxel2, const Eigen::Vector3d &radar_pos) {
    // **基本有效性检查**
    if(!voxel1 || !voxel2) return false;
    if(!voxel1->plane.is_plane || !voxel2->plane.is_plane) return false;
    if(voxel1->octo_state != 0 || voxel2->octo_state != 0) return false;

    // **🚀 性能优化1：快速预筛选，避免昂贵的矩阵计算**

    // 快速层级检查
    int layer_diff = abs(voxel1->layer - voxel2->layer);
    if(layer_diff > 1) return false;

    // 快速距离检查
    Eigen::Vector3d center_diff = voxel1->plane.center - voxel2->plane.center;
    double center_distance = center_diff.norm();
    if(center_distance > 20.0) return false;  // 2米，快速拒绝明显不合理的候选

    // **🚀 性能优化2：安全地获取平面参数**
    Eigen::Vector3d normal1, normal2, center1, center2;
    Eigen::Matrix<double, 6, 6> plane_cov1, plane_cov2;

    if(!get_plane_params_safe(voxel1, radar_pos, normal1, center1, plane_cov1) ||
       !get_plane_params_safe(voxel2, radar_pos, normal2, center2, plane_cov2)) {
        return false;
    }

    // **🚀 性能优化3：快速角度预检查**
    double cos_angle = normal1.dot(normal2);
    // if(cos_angle < 0.9) return false;  // 约25度，快速拒绝角度差异过大的

    // **🚀 性能优化4：检查协方差矩阵有效性，避免无效计算**
    if(plane_cov1.hasNaN() || plane_cov2.hasNaN()) {
        // 回退到几何判断
        if(cos_angle < cos(5.0 * M_PI / 180.0)) return false;  // 15度
        double dist = abs(normal1.dot(center2 - center1));
        return dist < 0.1;  // 50cm
    }

    // **🚀 性能优化5：构建差异向量（保留马氏距离的核心）**
    Eigen::Vector3d normal_diff = normal1 - normal2;
    center_diff = center1 - center2;  // 重新计算，使用调整后的中心

    Eigen::Matrix<double, 6, 1> abd_bias;
    abd_bias.head(3) = normal_diff;
    abd_bias.tail(3) = center_diff;

    // **🚀 性能优化6：组合协方差矩阵**
    Eigen::Matrix<double, 6, 6> combined_cov = plane_cov1 + plane_cov2;

    // 添加正则化项确保数值稳定性
    double regularization = 1e-8;
    combined_cov += regularization * Eigen::Matrix<double, 6, 6>::Identity();

    // **🚀 性能优化7：使用Cholesky分解替代LU分解（更快且数值稳定）**
    Eigen::LLT<Eigen::Matrix<double, 6, 6>> cholesky_decomp(combined_cov);
    if(cholesky_decomp.info() != Eigen::Success) {
        // Cholesky分解失败，回退到几何判断
        if(cos_angle < cos(3.0 * M_PI / 180.0)) return false;
        double dist = abs(normal1.dot(center_diff));
        return dist < 0.03;
    }

    // **🚀 性能优化8：直接求解线性系统，避免显式求逆**
    Eigen::Matrix<double, 6, 1> solved = cholesky_decomp.solve(abd_bias);
    double mahalanobis_distance_squared = abd_bias.dot(solved);

    if(mahalanobis_distance_squared < 0) {
        // 数值问题，回退到几何判断
        if(cos_angle < cos(5.0 * M_PI / 180.0)) return false;
        double dist = abs(normal1.dot(center_diff));
        return dist < 0.08;
    }

    double m_distance = sqrt(mahalanobis_distance_squared);

    // **🚀 保留原有的马氏距离阈值和额外约束**
    static constexpr double MAHALANOBIS_THRESHOLD = 0.008;

    // VoxelMap++风格的额外约束
    bool additional_constraint_passed = false;
    if(abs(normal_diff[0]) < 0.15 && abs(normal_diff[1]) < 0.15) {
        additional_constraint_passed = true;
    }

    // 几何约束检查
    bool geometric_constraints_passed = true;
    if(center_distance > 2.0) {  // 2米
        geometric_constraints_passed = false;
    }

    // 马氏距离判断
    bool mahalanobis_test_passed = (m_distance < MAHALANOBIS_THRESHOLD);

    // 最终判断（保留原有逻辑）
    return (additional_constraint_passed || mahalanobis_test_passed) && geometric_constraints_passed;
}
*/

// **🚀 高性能优化：体素合并判断（保持所有功能逻辑不变）**
bool VoxelMergeManager::can_merge_voxels_optimized_mahalanobis(OctoTree* voxel1, OctoTree* voxel2, const Eigen::Vector3d &radar_pos) {
    // **快速失败检查1：基本有效性验证**
    if(!voxel1 || !voxel2) return false;
    if(!voxel1->plane.is_plane || !voxel2->plane.is_plane) return false;
    if(voxel1->octo_state != 0 || voxel2->octo_state != 0) return false;

    // **🚀 性能优化1：预计算常量和静态变量（避免重复计算）**
    static const double NORMAL_ANGLE_THRESHOLD = 8.0;
    static const double cos_angle_threshold = cos(NORMAL_ANGLE_THRESHOLD * M_PI / 180.0);
    static const double MAHALANOBIS_THRESHOLD = 0.005;
    static const double MAHALANOBIS_THRESHOLD_SQUARED = MAHALANOBIS_THRESHOLD * MAHALANOBIS_THRESHOLD;
    static const double MAX_CENTER_DISTANCE = 2.0;
    static const double MAX_CENTER_DISTANCE_SQUARED = MAX_CENTER_DISTANCE * MAX_CENTER_DISTANCE;
    static const double MAX_PLANE_DISTANCE = 0.05;
    static const double regularization = 1e-8;
    static const Eigen::Matrix<double, 6, 6> identity_6x6 = Eigen::Matrix<double, 6, 6>::Identity();

    // **🚀 性能优化2：线程局部存储，避免频繁内存分配**
    thread_local static Eigen::Matrix<double, 6, 1> abd_bias;
    thread_local static Eigen::Matrix<double, 6, 6> combined_cov;
    thread_local static Eigen::Matrix<double, 6, 1> solved_vector;

    // **获取平面参数（使用const引用避免拷贝）**
    const Eigen::Vector3d& normal1_raw = voxel1->plane.normal;
    const Eigen::Vector3d& normal2_raw = voxel2->plane.normal;
    const Eigen::Vector3d& center1 = voxel1->plane.center;
    const Eigen::Vector3d& center2 = voxel2->plane.center;

    // **🚀 性能优化3：快速失败检查2 - 中心距离粗筛（最便宜的检查）**
    const Eigen::Vector3d center_diff = center1 - center2;
    const double center_distance_squared = center_diff.squaredNorm();
    if(center_distance_squared > MAX_CENTER_DISTANCE_SQUARED) {
        return false;  // 早期退出：距离太远
    }

    // **🚀 性能优化4：快速失败检查3 - 层级兼容性检查（避免昂贵的法向量计算）**
    const int layer_diff = abs(voxel1->layer - voxel2->layer);
    if(layer_diff > 1) {
        return false;  // 早期退出：层级差异太大
    }

    // **计算雷达朝向的法向量（只有通过前面检查才计算）**
    const Eigen::Vector3d normal1 = compute_radar_facing_normal(normal1_raw, center1, radar_pos);
    const Eigen::Vector3d normal2 = compute_radar_facing_normal(normal2_raw, center2, radar_pos);

    // **🚀 性能优化5：快速失败检查4 - 法向量角度检查**
    const double cos_angle = normal1.dot(normal2);
    if(cos_angle < cos_angle_threshold) {
        return false;  // 早期退出：角度太大
    }

    // **🚀 性能优化6：快速失败检查5 - 平面间垂直距离检查**
    const double plane_distance = abs(normal1.dot(center_diff));
    if(plane_distance > MAX_PLANE_DISTANCE) {
        return false;  // 早期退出：平面距离太大
    }

    // **获取协方差矩阵（使用const引用避免拷贝）**
    const Eigen::Matrix<double, 6, 6>& plane_cov1 = voxel1->plane.plane_var;
    const Eigen::Matrix<double, 6, 6>& plane_cov2 = voxel2->plane.plane_var;

    // **🚀 性能优化7：快速失败检查6 - 协方差矩阵有效性（使用更快的检查）**
    if(plane_cov1.hasNaN() || plane_cov2.hasNaN()) {
        return false;  // 早期退出：无效协方差矩阵
    }

    // **🚀 性能优化8：额外约束快速检查（可能避免马氏距离计算）**
    const Eigen::Vector3d normal_diff = normal1 - normal2;
    const bool additional_constraint_passed = (abs(normal_diff[0]) < 0.1 && abs(normal_diff[1]) < 0.1);

    // **🚀 性能优化9：如果额外约束通过，可以跳过昂贵的马氏距离计算**
    if(additional_constraint_passed) {
        return true;  // 早期成功退出
    }

    // **构建6维差异向量（重用线程局部变量）**
    abd_bias.head(3) = normal_diff;
    abd_bias.tail(3) = center_diff;

    // **🚀 性能优化10：快速失败检查7 - 差异向量范数预检查**
    const double bias_norm_squared = abd_bias.squaredNorm();
    if(bias_norm_squared > 1.0) {  // 如果差异太大，直接拒绝
        return false;  // 早期退出：差异向量太大
    }

    // **🚀 性能优化11：智能组合协方差矩阵计算**
    combined_cov.noalias() = plane_cov1 + plane_cov2;  // 使用noalias()避免临时对象
    combined_cov.diagonal().array() += regularization;  // 更高效的正则化

    // **🚀 性能优化12：优化的马氏距离计算 - 智能分解方法选择**
    double mahalanobis_distance_squared = 0.0;
    bool computation_success = false;

    // **方法1：优先使用Cholesky分解（最快，适用于正定矩阵）**
    Eigen::LLT<Eigen::Matrix<double, 6, 6>> chol_decomp(combined_cov);
    if(chol_decomp.info() == Eigen::Success) {
        chol_decomp.solveInPlace(abd_bias);  // 原地求解，避免临时对象
        mahalanobis_distance_squared = abd_bias.dot(abd_bias);  // 重用abd_bias
        computation_success = true;
    } else {
        // **方法2：LDLT分解（次优选择，适用于半正定矩阵）**
        Eigen::LDLT<Eigen::Matrix<double, 6, 6>> ldlt_decomp(combined_cov);
        if(ldlt_decomp.info() == Eigen::Success && ldlt_decomp.isPositive()) {
            solved_vector = ldlt_decomp.solve(abd_bias);
            mahalanobis_distance_squared = abd_bias.dot(solved_vector);
            computation_success = true;
        } else {
            // **方法3：LU分解（最后手段，最慢但最稳健）**
            Eigen::PartialPivLU<Eigen::Matrix<double, 6, 6>> lu_decomp(combined_cov);
            if(lu_decomp.determinant() > 1e-12) {
                solved_vector = lu_decomp.solve(abd_bias);
                mahalanobis_distance_squared = abd_bias.dot(solved_vector);
                computation_success = true;
            }
        }
    }

    // **🚀 性能优化13：马氏距离判断 - 直接比较平方值，避免开方运算**
    if(computation_success) {
        return (mahalanobis_distance_squared <= MAHALANOBIS_THRESHOLD_SQUARED);
    }

    // **如果马氏距离计算失败，返回false（保持原有逻辑）**
    return false;
}

// **外部函数实现：为voxel_map.hpp提供接口**
bool should_contribute_merge_constraint(int group_id) {
    return VoxelMergeManager::getInstance().should_contribute_constraint(group_id);
}

// **🔧 新增：验证合并组有效性**
bool is_merge_group_valid(int group_id) {
    return VoxelMergeManager::getInstance().is_group_valid(group_id);
}

void validate_merge_group_consistency(int group_id) {
    VoxelMergeManager::getInstance().validate_merge_group_consistency(group_id);
}

// **🚀 新增：全局函数访问预计算数据**
bool get_precomputed_merge_data_global(int group_id,
                                      vector<PointCluster>& merged_pcrs,
                                      PointCluster& merged_pcr_fix) {
    VoxelMergeManager::PrecomputedMergeData merge_data;
    if(VoxelMergeManager::getInstance().get_precomputed_merge_data(group_id, merge_data)) {
        merged_pcrs = merge_data.merged_pcrs;
        merged_pcr_fix = merge_data.merged_pcr_fix;
        return true;
    }
    return false;
}

// **🔧 新增：重置约束去重标志的全局函数**
void reset_merge_constraint_flags() {
    VoxelMergeManager::reset_constraint_flags();
}

void reset_constraint_flags() {
    VoxelMergeManager::getInstance().reset_constraint_flags();
}

void validate_merge_consistency(const unordered_map<VOXEL_LOC, OctoTree*>& surf_map) {
    VoxelMergeManager::getInstance().validate_overall_consistency(surf_map);
}

// **🚀 性能优化：收集合并组的完整pcrs数据（增强错误处理）**
bool collect_merged_group_pcrs_data(int group_id,
                                   const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                   vector<PointCluster>& merged_pcrs,
                                   PointCluster& merged_pcr_fix,
                                   int wdsize) {
    auto& manager = VoxelMergeManager::getInstance();

    // 获取合并组的所有成员体素位置
    vector<VOXEL_LOC> member_locations;
    if(!manager.get_group_member_locations(group_id, member_locations)) {
        ROS_DEBUG("Group %d not found or invalid", group_id);
        return false;  // 组不存在或无效
    }

    if(member_locations.size() < 2) {
        ROS_DEBUG("Group %d has insufficient members: %zu", group_id, member_locations.size());
        return false;  // 成员太少
    }

    // 初始化输出数据
    merged_pcrs.resize(wdsize);
    for(int i = 0; i < wdsize; i++) {
        merged_pcrs[i].clear();
    }
    merged_pcr_fix.clear();

    int valid_members = 0;
    int invalid_voxels = 0;

    // 遍历所有成员体素，收集它们的pcrs数据
    for(const auto& loc : member_locations) {
        auto it = surf_map.find(loc);
        if(it == surf_map.end() || !it->second) {
            invalid_voxels++;
            continue;
        }

        OctoTree* voxel = it->second;
        // **🔧 关键修复：大幅放宽数据收集的有效性检查**
        // 1. 不检查octo_state，因为已经细分的体素仍可能有有效的合并信息
        // 2. 不强制要求sw存在，因为边缘化过程中sw可能被临时清理
        // 3. 优先使用merged_plane，如果不存在则使用原始plane
        
        bool has_valid_plane = false;
        if(voxel->is_merged && voxel->merged_plane.is_plane) {
            has_valid_plane = true;
        } else if(voxel->plane.is_plane) {
            has_valid_plane = true;
        }

        if(!has_valid_plane) {
            invalid_voxels++;
            continue;
        }

        // **新增：尝试多种数据源**
        bool collected_frame_data = false;
        bool collected_fixed_data = false;

        // 优先尝试从滑动窗口收集数据
        if(voxel->sw != nullptr && voxel->isexist) {
            extern int* mp;  // 帧索引映射
            for(int i = 0; i < wdsize; i++) {
                const PointCluster& frame_data = voxel->sw->pcrs_local[mp[i]];
                if(frame_data.N > 0) {
                    merged_pcrs[i] += frame_data;  // 使用PointCluster的+=操作符
                    collected_frame_data = true;
                }
            }
        }
        
        // 收集固定点数据（更容易获得）
        if(voxel->pcr_fix.N > 0) {
            merged_pcr_fix += voxel->pcr_fix;
            collected_fixed_data = true;
        } else if(voxel->pcr_add.N > 0) {
            // 如果没有pcr_fix，使用pcr_add作为备选
            merged_pcr_fix += voxel->pcr_add;
            collected_fixed_data = true;
        }

        // 只要收集到任何有效数据就算成功
        if(collected_frame_data || collected_fixed_data) {
            valid_members++;
        } else {
            invalid_voxels++;
        }
    }

    // **🔧 进一步放宽成功标准：只要有1个以上有效成员即可**
    if(valid_members >= 1) {
        ROS_DEBUG("Group %d data collection SUCCESS: %d valid members, %d invalid, %zu total",
                 group_id, valid_members, invalid_voxels, member_locations.size());
        return true;
    }

    // **增强调试信息：详细报告失败原因**
    ROS_DEBUG("Group %d data collection FAILED: %d valid, %d invalid of %zu total members",
             group_id, valid_members, invalid_voxels, member_locations.size());
    
    // 详细分析失败原因
    int missing_voxels = 0, no_plane_voxels = 0, no_data_voxels = 0;
    for(const auto& loc : member_locations) {
        auto it = surf_map.find(loc);
        if(it == surf_map.end() || !it->second) {
            missing_voxels++;
            continue;
        }
        
        OctoTree* voxel = it->second;
        bool has_plane = (voxel->is_merged && voxel->merged_plane.is_plane) || voxel->plane.is_plane;
        if(!has_plane) {
            no_plane_voxels++;
            continue;
        }
        
        bool has_data = (voxel->sw != nullptr && voxel->isexist) || 
                       (voxel->pcr_fix.N > 0) || (voxel->pcr_add.N > 0);
        if(!has_data) {
            no_data_voxels++;
        }
    }
    
    ROS_DEBUG("Failure breakdown - Missing: %d, No plane: %d, No data: %d", 
             missing_voxels, no_plane_voxels, no_data_voxels);
    
    return false;
}

// **新增：标记函数实现（修复链接错误）**
void mark_voxel_updated_for_merge(const VOXEL_LOC& loc) {
    VoxelMergeManager::getInstance().mark_voxel_updated(loc);
}

void mark_voxel_created_for_merge(const VOXEL_LOC& loc) {
    VoxelMergeManager::getInstance().mark_voxel_created(loc);
}

// **🔍 Performance Analysis Function**
void print_merge_performance_stats(const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                  double process_time, double prepare_time, double total_time) {
    // Count merged vs normal voxels
    int merged_voxels = 0;
    int normal_voxels = 0;
    int total_groups = 0;
    int active_groups = 0;

    for(const auto& pair : surf_map) {
        if(pair.second && pair.second->isexist) {
            if(pair.second->is_merged) {
                merged_voxels++;
            } else {
                normal_voxels++;
            }
        }
    }

    // Get merge group statistics from VoxelMergeManager
    auto& manager = VoxelMergeManager::getInstance();
    // Note: We can't access private members directly, so we'll estimate

    // Print performance summary in English
    // ROS_INFO("MERGE_PERF: Process=%.1fms Prepare=%.1fms Total=%.1fms | Merged=%d Normal=%d",
    //          process_time, prepare_time, total_time, merged_voxels, normal_voxels);
}

//!Testing Merge Judge - 新增：单个体素与组的严格全局验证函数
bool VoxelMergeManager::can_voxel_merge_with_group_strict(OctoTree* new_voxel,
                                                        const MergeGroupController& group,
                                                        const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                                        const Eigen::Vector3d& radar_pos) {
    // **验证1：检查组的基本有效性**
    if(!group.is_active || group.active_members.size() < 1) {
        ROS_DEBUG("Group validation failed: group inactive or empty");
        return false;
    }

    // **验证2：检查组大小限制，防止过度合并**
    if(group.active_members.size() >= MAX_GROUP_SIZE) {
        ROS_DEBUG("Group validation failed: group size %zu exceeds limit %d",
                 group.active_members.size(), MAX_GROUP_SIZE);
        return false;
    }

    // **优化：将active_members转换为vector以支持并行访问**
    vector<VOXEL_LOC> member_locations(group.active_members.begin(), group.active_members.end());
    
    // **验证3：新体素必须与组中每个成员都满足合并条件 - 并行化优化**
    int valid_members = 0;
    int invalid_members = 0;
    std::atomic<bool> all_valid{true};  // 原子变量用于早期退出
    std::mutex stats_mutex;  // 保护统计变量

    #pragma omp parallel for schedule(dynamic)
    for(int i = 0; i < member_locations.size(); i++) {
        if(!all_valid.load()) continue;  // 早期退出：如果已经发现不兼容的成员
        
        const auto& member_loc = member_locations[i];
        auto it = surf_map.find(member_loc);
        if(it == surf_map.end() || !it->second) {
            std::lock_guard<std::mutex> lock(stats_mutex);
            invalid_members++;
            continue;  // 跳过不存在的成员
        }

        OctoTree* member_voxel = it->second;

        // 检查成员体素的基本有效性
        if(member_voxel->octo_state != 0 || !member_voxel->plane.is_plane) {
            std::lock_guard<std::mutex> lock(stats_mutex);
            invalid_members++;
            continue;  // 跳过无效成员
        }

        // **关键验证：新体素必须与当前成员满足合并条件（使用优化版本）**
        if(!can_merge_voxels_optimized_mahalanobis(new_voxel, member_voxel, radar_pos)) {
            ROS_DEBUG("Strict validation failed: new voxel incompatible with member [%ld,%ld,%ld]",
                     member_loc.x, member_loc.y, member_loc.z);
            all_valid.store(false);  // 设置失败标志，其他线程会提前退出
            continue;
        }

        {
            std::lock_guard<std::mutex> lock(stats_mutex);
        valid_members++;
        }
    }

    // **验证4：确保组中有足够的有效成员**
    if(!all_valid.load() || valid_members < 1) {
    if(valid_members < 1) {
        ROS_DEBUG("Group validation failed: no valid members found");
        }
        return false;
    }

    ROS_DEBUG("Strict validation passed: new voxel compatible with all %d valid members", valid_members);
    return true;
}

//!Testing Merge Judge - 新增：两个组之间的严格全局验证函数
bool VoxelMergeManager::can_groups_merge_strict(const MergeGroupController& group1,
                                               const MergeGroupController& group2,
                                               const unordered_map<VOXEL_LOC, OctoTree*>& surf_map,
                                               const Eigen::Vector3d& radar_pos) {
    // **验证1：检查两个组的基本有效性**
    if(!group1.is_active || !group2.is_active) {
        ROS_DEBUG("Group merge validation failed: one or both groups inactive");
        return false;
    }

    if(group1.active_members.size() < 1 || group2.active_members.size() < 1) {
        ROS_DEBUG("Group merge validation failed: one or both groups empty");
        return false;
    }

    // **验证2：检查合并后的组大小限制**
    size_t total_size = group1.active_members.size() + group2.active_members.size();
    if(total_size > MAX_GROUP_SIZE) {
        ROS_DEBUG("Group merge validation failed: combined size %zu exceeds limit %d",
                 total_size, MAX_GROUP_SIZE);
        return false;
    }

    // **优化：预处理有效体素，转换为vector以支持并行访问**
    vector<pair<VOXEL_LOC, OctoTree*>> valid_voxels1, valid_voxels2;
    valid_voxels1.reserve(group1.active_members.size());
    valid_voxels2.reserve(group2.active_members.size());

    // 预先收集group1中的有效体素
    for(const auto& loc1 : group1.active_members) {
        auto it1 = surf_map.find(loc1);
        if(it1 != surf_map.end() && it1->second && 
           it1->second->octo_state == 0 && it1->second->plane.is_plane) {
            valid_voxels1.emplace_back(loc1, it1->second);
        }
    }

    // 预先收集group2中的有效体素
        for(const auto& loc2 : group2.active_members) {
            auto it2 = surf_map.find(loc2);
        if(it2 != surf_map.end() && it2->second && 
           it2->second->octo_state == 0 && it2->second->plane.is_plane) {
            valid_voxels2.emplace_back(loc2, it2->second);
        }
    }

    if(valid_voxels1.empty() || valid_voxels2.empty()) {
        ROS_DEBUG("Group merge validation failed: no valid voxels in one or both groups");
        return false;
    }

    // **验证3：group1中的每个成员都必须与group2中的每个成员满足合并条件 - 并行化优化**
    // 这是防止链式漂移的关键验证
    int total_checks = 0;
    int passed_checks = 0;
    std::atomic<bool> all_compatible{true};  // 原子变量用于早期退出
    std::mutex stats_mutex;  // 保护统计变量

    #pragma omp parallel for schedule(dynamic) collapse(2)
    for(int i = 0; i < valid_voxels1.size(); i++) {
        for(int j = 0; j < valid_voxels2.size(); j++) {
            if(!all_compatible.load()) continue;  // 早期退出：如果已经发现不兼容的体素对

            const auto& pair1 = valid_voxels1[i];
            const VOXEL_LOC& loc1 = pair1.first;
            OctoTree* voxel1 = pair1.second;

            const auto& pair2 = valid_voxels2[j];
            const VOXEL_LOC& loc2 = pair2.first;
            OctoTree* voxel2 = pair2.second;

            {
                std::lock_guard<std::mutex> lock(stats_mutex);
                total_checks++;
            }

            // **关键验证：group1的成员必须与group2的成员满足合并条件（使用优化版本）**
            if(can_merge_voxels_optimized_mahalanobis(voxel1, voxel2, radar_pos)) {
                std::lock_guard<std::mutex> lock(stats_mutex);
                passed_checks++;
            } else {
                ROS_DEBUG("Group merge validation failed: voxel [%ld,%ld,%ld] from group1 incompatible with voxel [%ld,%ld,%ld] from group2",
                         loc1.x, loc1.y, loc1.z, loc2.x, loc2.y, loc2.z);
                all_compatible.store(false);  // 设置失败标志，其他线程会提前退出
                continue;
            }
        }
    }

    // **验证4：确保进行了足够的检查**
    if(!all_compatible.load() || total_checks < 1) {
    if(total_checks < 1) {
        ROS_DEBUG("Group merge validation failed: no valid voxel pairs found for checking");
        }
        return false;
    }

    ROS_DEBUG("Group merge validation passed: all %d voxel pairs compatible", total_checks);
    return true;
}

#endif // VOXEL_MERGE_HPP
