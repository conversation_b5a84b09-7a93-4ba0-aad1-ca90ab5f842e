#!/bin/bash

# **🚀 体素合并性能测试脚本**
# 用于测试优化前后的性能差异

echo "=== 体素合并性能测试 ==="
echo "测试时间: $(date)"

# 编译项目
echo "1. 编译项目..."
cd /home/<USER>/ws_voxel_slam2_vscode
catkin_make -DCMAKE_BUILD_TYPE=RelWithDebInfo

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 设置环境
source devel/setup.bash

# 创建测试结果目录
TEST_DIR="performance_test_$(date +%Y%m%d_%H%M%S)"
mkdir -p $TEST_DIR

echo "2. 开始性能测试..."
echo "测试结果将保存到: $TEST_DIR"

# 运行测试（30秒）
echo "启动SLAM系统进行30秒测试..."
timeout 30s roslaunch voxel_slam vxlm_hesai.launch > $TEST_DIR/slam_output.log 2>&1 &
SLAM_PID=$!

# 等待系统启动
sleep 5

# 监控性能
echo "监控系统性能..."
top -p $SLAM_PID -b -n 6 -d 5 > $TEST_DIR/cpu_usage.log &

# 等待测试完成
wait $SLAM_PID

echo "3. 分析测试结果..."

# 提取关键性能指标
echo "=== 性能分析报告 ===" > $TEST_DIR/performance_report.txt
echo "测试时间: $(date)" >> $TEST_DIR/performance_report.txt
echo "" >> $TEST_DIR/performance_report.txt

# 分析SLAM输出
if [ -f $TEST_DIR/slam_output.log ]; then
    echo "--- 体素合并统计 ---" >> $TEST_DIR/performance_report.txt
    grep "MERGE_DEBUG" $TEST_DIR/slam_output.log | tail -10 >> $TEST_DIR/performance_report.txt
    echo "" >> $TEST_DIR/performance_report.txt
    
    echo "--- 性能统计 ---" >> $TEST_DIR/performance_report.txt
    grep "PERF\[" $TEST_DIR/slam_output.log | tail -10 >> $TEST_DIR/performance_report.txt
    echo "" >> $TEST_DIR/performance_report.txt
    
    echo "--- BA分析 ---" >> $TEST_DIR/performance_report.txt
    grep "BA_ANALYSIS" $TEST_DIR/slam_output.log | tail -5 >> $TEST_DIR/performance_report.txt
    echo "" >> $TEST_DIR/performance_report.txt
    
    echo "--- 约束问题警告 ---" >> $TEST_DIR/performance_report.txt
    grep "CONSTRAINT_ISSUE" $TEST_DIR/slam_output.log >> $TEST_DIR/performance_report.txt
    echo "" >> $TEST_DIR/performance_report.txt
    
    # 计算平均性能
    echo "--- 平均性能计算 ---" >> $TEST_DIR/performance_report.txt
    
    # 提取总时间
    TOTAL_TIMES=$(grep "PERF\[" $TEST_DIR/slam_output.log | grep -o "Total=[0-9.]*" | cut -d= -f2)
    if [ ! -z "$TOTAL_TIMES" ]; then
        AVG_TOTAL=$(echo "$TOTAL_TIMES" | awk '{sum+=$1; count++} END {if(count>0) print sum/count; else print 0}')
        echo "平均总时间: ${AVG_TOTAL} ms" >> $TEST_DIR/performance_report.txt
    fi
    
    # 提取合并时间
    MERGE_TIMES=$(grep "PERF\[" $TEST_DIR/slam_output.log | grep -o "Merge=[0-9.]*" | cut -d= -f2)
    if [ ! -z "$MERGE_TIMES" ]; then
        AVG_MERGE=$(echo "$MERGE_TIMES" | awk '{sum+=$1; count++} END {if(count>0) print sum/count; else print 0}')
        echo "平均合并时间: ${AVG_MERGE} ms" >> $TEST_DIR/performance_report.txt
    fi
    
    # 提取BA时间
    BA_TIMES=$(grep "PERF\[" $TEST_DIR/slam_output.log | grep -o "BA=[0-9.]*" | cut -d= -f2)
    if [ ! -z "$BA_TIMES" ]; then
        AVG_BA=$(echo "$BA_TIMES" | awk '{sum+=$1; count++} END {if(count>0) print sum/count; else print 0}')
        echo "平均BA时间: ${AVG_BA} ms" >> $TEST_DIR/performance_report.txt
    fi
    
    # 统计合并体素数量
    MERGED_COUNTS=$(grep "MERGE_DEBUG" $TEST_DIR/slam_output.log | grep -o "returned [0-9]*" | cut -d' ' -f2)
    if [ ! -z "$MERGED_COUNTS" ]; then
        AVG_MERGED=$(echo "$MERGED_COUNTS" | awk '{sum+=$1; count++} END {if(count>0) print sum/count; else print 0}')
        echo "平均合并体素数: ${AVG_MERGED}" >> $TEST_DIR/performance_report.txt
    fi
fi

echo "4. 测试完成"
echo "详细报告: $TEST_DIR/performance_report.txt"
echo ""
echo "=== 快速摘要 ==="
if [ -f $TEST_DIR/performance_report.txt ]; then
    grep "平均" $TEST_DIR/performance_report.txt
fi

echo ""
echo "建议："
echo "1. 如果平均合并体素数很少(<50)，说明合并策略过于保守"
echo "2. 如果BA时间仍然很高，说明约束去重机制可能有问题"
echo "3. 如果总时间比原始版本慢，需要进一步优化"
